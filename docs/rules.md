# Development Rules & Standards

## Ultimate Electrical Designer

**Document Version:** 2.0 **Last Updated:** August 2025 **Recent Updates:** Critical Infrastructure Fixes Implemented

---

## Overview

This document establishes comprehensive development rules that ensure engineering-grade quality, maintain architectural
consistency, and enforce professional standards suitable for mission-critical electrical design applications.

---

## Zero Tolerance Policies

### 1. Code Quality Standards

**Policy:** Zero tolerance for code quality violations that compromise system reliability or maintainability.

#### Backend (Python) Requirements

- **100% MyPy compliance** for all production code
- **Zero Ruff linting errors** in committed code
- **Complete type hints** for all public APIs and critical internal functions
- **Docstring coverage** for all public methods using Google style format
- **Email normalization enforcement** for all email fields using lowercase normalization and case-insensitive uniqueness
  validation
- **Application-level string length validation** for all user input fields with specific error messages and boundary
  condition testing

#### Frontend (TypeScript) Requirements

- **Zero ESLint errors** in committed code
- **Zero Prettier formatting violations** in committed code
- **Strict TypeScript mode** with no `any` types in production code
- **Complete type annotations** for all component props and API interfaces
- **Frameworks & Libraries**: Next.js 15 (App Router), React Query 5, Zustand 5, MSW for API mocking
- **Testing**: Vitest (unit), Testing Library (DOM), Playwright (e2e)
- **Path Aliases**: Use TypeScript path aliases as configured in tsconfig for clean imports
- **Component Architecture**: All UI must conform to the Atomic Design System. See
  [Atomic Design System Guide](developer-guides/client/atomid-design-guide.md) for component taxonomy, contracts, and
  usage rules.

### 2. Testing Standards

**All testing standards, requirements, and policies are documented in:**

📋 **[TESTING.md](TESTING.md#2-testing-standards-requirements)** - Authoritative Testing Standards

This includes comprehensive coverage of:

- **Zero tolerance policies** for test failures and quality violations
- **Coverage requirements** (95%+ pass rate, 100% critical logic, 85%+ other modules)
- **Quality gates** and success metrics
- **Real database testing** requirements and patterns

## Methodologies and Process Classifications

### Test-Driven Development (TDD) — Development Methodology

- Definition: A development methodology in which tests are written before production code, guiding implementation and
  design.
- Enforcement: Unit and integration tests must be authored alongside code; changes that reduce coverage below thresholds
  are prohibited.
- Reference: docs/TESTING.md for coverage targets and workflows.

### 5-Phase Implementation/Testing — Project Management and Development Methodology

- Definition: The project’s mandated end-to-end methodology governing how work is executed from discovery through
  handover.
- Phases: Discovery & Analysis, Task Planning, Implementation, Verification, Documentation & Handover.
- Enforcement: All work must follow this methodology; see docs/workflows.md for details.

---

---

## Architectural Rules

### 1. 5-Layer Architecture Compliance

**Rule:** All backend code must strictly adhere to the 5-layer architecture pattern.

#### Layer Responsibilities

1. **API Layer** (`src/api/`): HTTP request/response handling, input validation, authentication
2. **Service Layer** (`src/core/services/`): Business logic, workflow orchestration, transaction management
3. **Repository Layer** (`src/core/repositories/`): Data access abstraction, query optimization
4. **Model Layer** (`src/core/models/`): Data structure definition, relationships, constraints
5. **Schema Layer** (`src/core/schemas/`): Request/response validation, data transformation

#### Dependency Rules

- **Downward dependencies only**: Higher layers may depend on lower layers, never upward
- **No layer skipping**: Each layer must interact only with adjacent layers
- **Interface segregation**: Use dependency injection for loose coupling between layers

### 2. Unified Error Handling

**Rule:** All error handling must use the unified error handling system.

#### Required Decorators

```python
# Service layer methods
@handle_service_errors("method_name")
@monitor_service_performance("method_name")

# API endpoints
@handle_api_errors("endpoint_name")

# Database operations
@handle_database_errors("operation_name")

# Security operations
@handle_security_errors("security_operation")
```

### 3. Frontend Module Organization

**Rule:** Frontend code must follow Domain-Driven Design (DDD) principles with clear module boundaries.

#### Module Structure Requirements

```python
src/modules/{domain}/
├── components/     # Domain-specific UI components
├── hooks/          # Domain-specific React hooks
├── services/       # Domain API services
├── types/          # Domain type definitions
└── __tests__/      # Domain-specific tests
```

### 3. Schema Design Patterns

**Rule:** All Pydantic schemas must follow established patterns for type safety and consistency.

#### Generic Pagination Schema Pattern

**Mandatory Implementation:**

```python
from typing import TypeVar, Generic, List, Optional
from pydantic import BaseModel, Field

# Type variable for generic pagination
T = TypeVar("T")

class PaginationSchema(BaseModel):
    """Schema for pagination parameters."""

    page: int = Field(1, ge=1, description="Page number (1-based)")
    size: int = Field(20, ge=1, le=100, description="Items per page")
    total: Optional[int] = Field(None, description="Total number of items")
    pages: Optional[int] = Field(None, description="Total number of pages")

class PaginatedResponseSchema(BaseModel, Generic[T]):
    """Generic paginated response schema."""

    items: List[T] = Field(..., description="List of items")
    pagination: PaginationSchema = Field(..., description="Pagination information")

# Usage - REQUIRED for all paginated endpoints:
class EntityListResponseSchema(PaginatedResponseSchema[EntityReadSchema]):
    """Paginated response schema for entities."""
    pass
```

#### Schema Validation Requirements

- **Generic Types**: All paginated responses must use `PaginatedResponseSchema[T]`
- **Type Parameters**: Must specify exact entity type (e.g., `TaskReadSchema`, `ComponentReadSchema`)
- **MyPy Compliance**: All schemas must pass MyPy type checking without errors
- **ValidationInfo**: All field validators must include proper type annotations

**Example Validator Pattern:**

```python
from pydantic import field_validator, ValidationInfo

@field_validator("field_name")
@classmethod
def validate_field(cls, v: Optional[datetime], info: ValidationInfo) -> Optional[datetime]:
    """Validate field with proper type annotations."""
    # Validation logic here
    return v
```

#### Migration Requirements

- **Legacy Schemas**: All non-generic pagination schemas must be migrated to generic pattern
- **Type Safety**: Zero tolerance for missing type parameters in generic schemas
- **Consistency**: All endpoints must use the same pagination structure

---

## Code Quality Rules

### 1. Type Safety Requirements

**Rule:** Complete type safety across the entire codebase.

### 2. Documentation Standards

**Rule:** Complete documentation for all public APIs and complex business logic.

#### Required Documentation

- **API endpoints**: OpenAPI/Swagger documentation with examples
- **Component props**: TypeScript interfaces with JSDoc comments
- **Business logic**: Inline comments explaining engineering calculations
- **Configuration**: Environment variables and settings documentation

### 3. Performance Standards

**Rule:** All code must meet performance benchmarks for professional electrical design applications.

#### Performance Requirements

- **API Response Time**: < 200ms for standard operations
- **Calculation Performance**: < 500ms for complex electrical calculations
- **Memory Usage**: < 100MB for typical calculation operations
- **Database Queries**: < 100ms for standard CRUD operations

---

## Development Workflow Rules

### 1. Git Workflow Standards

**Rule:** Structured Git workflow with conventional commits and protected branches.

#### Commit Message Format

```text
type(scope): description

feat(auth): implement JWT token refresh mechanism
fix(calc): correct voltage drop calculation for aluminum conductors
docs(api): update component management endpoint documentation
test(e2e): add comprehensive authentication flow tests
```

#### Branch Protection Rules

- **Main branch**: Requires pull request review and passing CI/CD
- **Feature branches**: Must be up-to-date with main before merging
- **Hotfix branches**: Emergency fixes with expedited review process

### 2. Code Review Standards

**Rule:** All code changes require peer review with specific quality gates.

#### Review Checklist

- [ ] **Architecture compliance**: Follows 5-layer pattern and DDD principles
- [ ] **Type safety**: Complete type annotations and MyPy/TypeScript compliance
- [ ] **Test coverage**: Adequate test coverage with passing tests
- [ ] **Documentation**: Updated documentation for API changes
- [ ] **Performance**: No performance regressions introduced
- [ ] **Security**: No security vulnerabilities or authentication bypasses

### 3. Deployment Standards

**Rule:** Automated deployment with quality gates and rollback capabilities.

#### Deployment Pipeline

1. **Pre-deployment**: All tests pass, type checking passes, security scan passes
2. **Staging deployment**: Automated deployment to staging environment
3. **Integration testing**: Full E2E test suite execution
4. **Production deployment**: Blue-green deployment with health checks
5. **Post-deployment**: Monitoring and alerting validation

---

## Engineering Standards Compliance

### 1. IEC/EN Standards Integration

**Rule:** All electrical calculations must comply with relevant international standards.

#### Standards Implementation

- **IEC Standards**: International electrotechnical commission standards validation
- **EN Standards**: European electrical standards verification
- **Code Compliance**: Automated validation against local electrical codes

### 2. Calculation Accuracy Requirements

**Rule:** Engineering calculations must meet professional accuracy standards.

#### Accuracy Standards

- **Voltage Drop Calculations**: ±0.1% accuracy compared to manual calculations
- **Load Calculations**: ±0.5% accuracy for demand factor applications
- **Short Circuit Analysis**: ±1% accuracy for fault current calculations
- **Heat Tracing Calculations**: ±2% accuracy for thermal analysis

### 3. Professional Documentation Standards

**Rule:** All generated documentation must meet professional engineering standards.

#### Documentation Requirements

- **Calculation Reports**: Include methodology, assumptions, and references
- **Component Specifications**: Complete technical specifications with manufacturer data
- **Compliance Certificates**: Official documentation for regulatory submissions
- **Drawing Integration**: Seamless integration with CAD systems

---

## Critical Infrastructure Standards (August 2025)

### 1. Test Infrastructure Requirements

**Rule:** All new development must follow the established testing patterns implemented during the critical
infrastructure stabilization.

#### Mandatory Testing Patterns

- **Transaction Isolation**: All tests must run in isolated transactions with automatic rollback
- **Unique Data Generation**: All test data must include UUID-based unique identifiers
- **Async/Await Compliance**: All async operations must be properly awaited
- **Session Management**: Async fixtures must use async dependencies exclusively
- **Error Recovery**: All database operations must include transaction recovery mechanisms

#### Code Examples

**Required Test Data Pattern:**

```python
import uuid
unique_suffix = str(uuid.uuid4())[:8]
user = User(
    name=f"Test User {unique_suffix}",
    email=f"test.{unique_suffix}@example.com"
)
```

**Required Async Pattern:**

```python
# CORRECT
result = await async_repository.get_by_id(1)

# INCORRECT - Will cause test failures
result = async_repository.get_by_id(1)
```

### 2. Database Schema Compliance

**Rule:** All database queries must match the actual database schema exactly.

#### Schema Validation Requirements

- **Column Existence**: All referenced columns must exist in the target table
- **Table Names**: Use exact table names as defined in the database
- **Data Types**: Ensure query parameters match column data types
- **Constraints**: Respect all database constraints and relationships

### 3. Session Isolation Standards

**Rule:** Maintain strict separation between sync and async database sessions.

#### Session Management Requirements

- **Fixture Dependencies**: Async fixtures must only depend on other async fixtures
- **Session Scope**: Use function-scoped sessions for test isolation
- **Transaction Management**: Implement automatic rollback for all test transactions
- **Error Handling**: Include transaction recovery for aborted transactions

### 4. Quality Assurance Verification

**Rule:** All changes must maintain the established 82.9% test pass rate or higher.

#### Verification Requirements

- **Pre-commit Testing**: Run relevant test suites before committing changes
- **Infrastructure Integrity**: Ensure no regression in core testing infrastructure
- **Documentation Updates**: Update relevant documentation for any pattern changes
- **Performance Monitoring**: Monitor test execution times and resource usage

**Reference Documentation:**

- `docs/CRITICAL_FIXES_CHANGELOG.md` - Complete technical implementation details
- `docs/TESTING.md` - Updated testing standards and best practices
