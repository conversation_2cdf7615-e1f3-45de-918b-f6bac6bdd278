{"env": {"BASH_DEFAULT_TIMEOUT_MS": "900000", "BASH_MAX_TIMEOUT_MS": "900000"}, "permissions": {"allow": ["<PERSON><PERSON>(make:*)", "Bash(pnpm:*)", "Bash(pnpm dlx:*)", "<PERSON><PERSON>(uv run:*)", "<PERSON><PERSON>(uv run mypy:*)", "<PERSON><PERSON>(uv run ruff:*)", "Bash(uv run pytest:*)", "<PERSON><PERSON>(uv run ruff:*)", "<PERSON><PERSON>(find .)", "Bash(uv sync:*)", "Bash(docker-compose build:*)", "<PERSON><PERSON>(docker-compose up:*)", "Bash(find:*)", "Bash(ls:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(grep:*)", "Bash(cd /mnt/d/Projects/ued/server)", "Bash(uv run pytest -v -m \"not integration and not performance\" --cov=src --cov-report=term-missing --cov-report=xml --html=test-report-unit.html --self-contained-html)", "<PERSON><PERSON>(env)", "Bash(cd /mnt/d/Projects/ued/server/src)", "Bash(uv run alembic revision --autogenerate -m \"Synchronize database schema with models\")", "<PERSON><PERSON>(timeout:*)", "Bash(pnpm tsc:*)", "Bash(pnpm next lint:*)", "Bash(pnpm run:*)", "Bash(pnpm vitest:*)", "Bash(rm:*)", "<PERSON><PERSON>(sed:*)", "Bash(node:*)", "Bash(TESTING=true DATABASE_URL=\"postgresql://user:password@localhost:5433/ultimate_electrical_designer_test\" uv run alembic upgrade head)", "Bash(TESTING=true DATABASE_URL=\"postgresql://user:password@localhost:5433/ultimate_electrical_designer_test\" uv run alembic current)", "Bash(psql:*)", "Bash(TESTING=true DATABASE_URL=\"postgresql://user:password@localhost:5433/ultimate_electrical_designer_test\" uv run alembic downgrade base)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(PGPASSWORD=password psql:*)", "Bash(TESTING=true DATABASE_URL=\"postgresql://user:password@localhost:5433/ultimate_electrical_designer_test\" PGPASSWORD=password psql -h localhost -U user -p 5433 -d ultimate_electrical_designer_test -c \"\\dt\")", "Bash(echo \"Exit code: $?\")", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(true)", "Bash(TESTING=true DATABASE_URL=\"postgresql://user:password@localhost:5433/ultimate_electrical_designer_test\" uv run alembic downgrade -1)"], "deny": ["Bash(npm:*)", "Bash(npx:*)", "<PERSON><PERSON>(poetry run:*)", "Bash(pytest:*)", "<PERSON><PERSON>(python:*)"]}}