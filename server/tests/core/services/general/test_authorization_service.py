"""
Comprehensive test suite for Authorization Service

Tests all authorization functionality including permission checking,
role management, and RBAC operations following TDD methodology.
"""

import pytest
from unittest.mock import Mock, AsyncMock
from datetime import datetime, timedelta
from typing import List, Optional

from src.core.services.general.authorization_service import AuthorizationService
from src.core.repositories.general.role_repository import RoleRepository
from src.core.schemas.general.user_role_schemas import (
    RoleCreateSchema,
    RoleUpdateSchema,
    RoleReadSchema,
    UserRoleAssignmentCreateSchema,
    UserRoleAssignmentReadSchema,
)
from src.core.schemas.general.permission_schemas import (
    PermissionCreateSchema,
    PermissionReadSchema,
)
from src.core.models.general.permission import Permission, RolePermission
from src.core.models.general.user_role import UserRole
from src.core.models.general.user import User
from src.core.errors.exceptions import (
    NotFoundError,
    ValidationError,
    PermissionDeniedError,
    BusinessLogicError,
    UnauthorizedError,
    InvalidInputError,
    BaseApplicationException,
)


class TestAuthorizationService:
    """Test suite for AuthorizationService"""

    @pytest.fixture
    async def mock_role_repository(self):
        """Create mock role repository"""
        repo = Mock(spec=RoleRepository)
        return repo

    @pytest.fixture
    async def authorization_service(self, mock_role_repository):
        """Create AuthorizationService with mocked dependencies"""
        mock_user_repository = Mock()
        return AuthorizationService(
            role_repository=mock_role_repository,
            user_repository=mock_user_repository
        )

    @pytest.fixture
    def sample_user(self):
        """Sample user for testing"""
        return User(
            id=1,
            name="Test User",
            email="<EMAIL>",
            is_active=True,
            is_superuser=False,
        )

    @pytest.fixture
    def sample_superuser(self):
        """Sample superuser for testing"""
        return User(
            id=2,
            name="Super User",
            email="<EMAIL>",
            is_active=True,
            is_superuser=True,
        )

    @pytest.fixture
    def sample_role(self):
        """Sample role for testing"""
        import datetime
        now = datetime.datetime.utcnow()
        return UserRole(
            id=1,
            name="Test Role",
            description="Test role for unit tests",
            is_system_role=False,
            is_active=True,
            priority=100,
            created_at=now,
            updated_at=now,
            is_deleted=False,
        )

    @pytest.fixture
    def sample_permission(self):
        """Sample permission for testing"""
        import datetime
        now = datetime.datetime.utcnow()
        return Permission(
            id=1,
            name="Read Projects",
            resource="project",
            action="read",
            description="Permission to read projects",
            is_system_permission=False,
            is_active=True,
            created_at=now,
            updated_at=now,
            is_deleted=False,
        )

    async def test_check_user_permission_superuser_always_allowed(
        self, authorization_service, sample_superuser
    ):
        """Test that superusers always have all permissions"""
        # Act
        result = await authorization_service.check_user_permission(
            user_id=sample_superuser.id,
            resource="any_resource",
            action="any_action"
        )

        # Assert
        assert result is True

    async def test_check_user_permission_regular_user_with_permission(
        self, authorization_service, mock_role_repository, sample_user
    ):
        """Test permission check for regular user with permission"""
        # Arrange
        mock_role_repository.user_has_permission = AsyncMock(return_value=True)

        # Act
        result = await authorization_service.check_user_permission(
            user_id=sample_user.id,
            resource="project",
            action="read"
        )

        # Assert
        assert result is True
        mock_role_repository.user_has_permission.assert_called_once_with(
            sample_user.id, "project", "read"
        )

    async def test_check_user_permission_regular_user_without_permission(
        self, authorization_service, mock_role_repository, sample_user
    ):
        """Test permission check for regular user without permission"""
        # Arrange
        mock_role_repository.user_has_permission = AsyncMock(return_value=False)

        # Act
        result = await authorization_service.check_user_permission(
            user_id=sample_user.id,
            resource="project",
            action="delete"
        )

        # Assert
        assert result is False
        mock_role_repository.user_has_permission.assert_called_once_with(
            sample_user.id, "project", "delete"
        )

    async def test_create_role_success(
        self, authorization_service, mock_role_repository, sample_role
    ):
        """Test successful role creation"""
        # Arrange
        role_data = RoleCreateSchema(
            name="New Role",
            description="New test role",
            priority=50,
        )
        mock_role_repository.get_by_name = AsyncMock(return_value=None)
        mock_role_repository.create = AsyncMock(return_value=sample_role)

        # Act
        result = await authorization_service.create_role(role_data)

        # Assert
        assert result.name == sample_role.name
        assert result.description == sample_role.description
        mock_role_repository.create.assert_called_once()

    async def test_create_role_duplicate_name_raises_error(
        self, authorization_service, mock_role_repository
    ):
        """Test that creating role with duplicate name raises InvalidInputError"""
        # Arrange
        role_data = RoleCreateSchema(name="Existing Role")
        mock_role_repository.get_by_name = AsyncMock(
            return_value=UserRole(id=1, name="Existing Role", is_active=True, priority=100)
        )

        # Act & Assert
        with pytest.raises(InvalidInputError, match="Role with name 'Existing Role' already exists"):
            await authorization_service.create_role(role_data)

    async def test_get_role_by_id_success(
        self, authorization_service, mock_role_repository, sample_role
    ):
        """Test successful role retrieval by ID"""
        # Arrange
        mock_role_repository.get_with_permissions = AsyncMock(return_value=sample_role)

        # Act
        result = await authorization_service.get_role_by_id(sample_role.id)

        # Assert
        assert result.id == sample_role.id
        assert result.name == sample_role.name
        mock_role_repository.get_with_permissions.assert_called_once_with(sample_role.id)

    async def test_get_role_by_id_not_found(
        self, authorization_service, mock_role_repository
    ):
        """Test role retrieval with non-existent ID"""
        # Arrange
        role_id = 999
        mock_role_repository.get_with_permissions = AsyncMock(return_value=None)

        # Act & Assert
        with pytest.raises(NotFoundError, match="Role with ID 999 not found"):
            await authorization_service.get_role_by_id(role_id)

    async def test_update_role_success(
        self, authorization_service, mock_role_repository, sample_role
    ):
        """Test successful role update"""
        # Arrange
        update_data = RoleUpdateSchema(
            name="Updated Role",
            description="Updated description",
            priority=75,
        )
        updated_role = UserRole(**{
            **sample_role.__dict__,
            "name": "Updated Role",
            "description": "Updated description",
        })
        mock_role_repository.get_role_by_id = AsyncMock(return_value=sample_role)
        mock_role_repository.update_role = AsyncMock(return_value=updated_role)

        # Act
        result = await authorization_service.update_role(sample_role.id, update_data)

        # Assert
        assert result == updated_role
        mock_role_repository.update_role.assert_called_once()

    async def test_update_role_not_found(
        self, authorization_service, mock_role_repository
    ):
        """Test updating non-existent role"""
        # Arrange
        role_id = 999
        update_data = RoleUpdateSchema(name="Updated Role")
        mock_role_repository.get_role_by_id = AsyncMock(return_value=None)

        # Act & Assert
        with pytest.raises(NotFoundError, match="Role not found"):
            await authorization_service.update_role(role_id, update_data)

    async def test_delete_role_success(
        self, authorization_service, mock_role_repository, sample_role
    ):
        """Test successful role deletion"""
        # Arrange
        mock_role_repository.get_role_by_id = AsyncMock(return_value=sample_role)
        mock_role_repository.delete_role = AsyncMock(return_value=True)

        # Act
        result = await authorization_service.delete_role(sample_role.id)

        # Assert
        assert result is True
        mock_role_repository.delete_role.assert_called_once_with(sample_role.id)

    async def test_delete_system_role_raises_error(
        self, authorization_service, mock_role_repository
    ):
        """Test that deleting system role raises BusinessLogicError"""
        # Arrange
        system_role = UserRole(
            id=1,
            name="System Role",
            is_system_role=True,
            is_active=True,
            priority=1,
        )
        mock_role_repository.get_role_by_id = AsyncMock(return_value=system_role)

        # Act & Assert
        with pytest.raises(BusinessLogicError, match="Cannot delete system role"):
            await authorization_service.delete_role(system_role.id)

    async def test_create_permission_success(
        self, authorization_service, mock_role_repository, sample_permission
    ):
        """Test successful permission creation"""
        # Arrange
        permission_data = PermissionCreateSchema(
            name="Write Projects",
            resource="project",
            action="write",
            description="Permission to write projects",
        )
        mock_role_repository.create_permission = AsyncMock(return_value=sample_permission)

        # Act
        result = await authorization_service.create_permission(permission_data)

        # Assert
        assert result == sample_permission
        mock_role_repository.create_permission.assert_called_once()

    async def test_assign_permissions_to_role_success(
        self, authorization_service, mock_role_repository, sample_role
    ):
        """Test successful permission assignment to role"""
        # Arrange
        permission_ids = [1, 2, 3]
        updated_role = UserRole(**{**sample_role.__dict__, "permissions": "project.read,project.write,project.delete"})
        
        mock_role_repository.get_role_by_id = AsyncMock(return_value=sample_role)
        mock_role_repository.assign_permissions_to_role = AsyncMock(return_value=True)
        mock_role_repository.get_role_by_id_with_permissions = AsyncMock(return_value=updated_role)

        # Act
        result = await authorization_service.assign_permissions_to_role(
            role_id=sample_role.id,
            permission_ids=permission_ids,
            granted_by_user_id=1
        )

        # Assert
        assert result == updated_role
        mock_role_repository.assign_permissions_to_role.assert_called_once_with(
            sample_role.id, permission_ids, 1
        )

    async def test_assign_permissions_to_nonexistent_role_raises_error(
        self, authorization_service, mock_role_repository
    ):
        """Test that assigning permissions to non-existent role raises NotFoundError"""
        # Arrange
        role_id = 999
        permission_ids = [1, 2, 3]
        mock_role_repository.get_role_by_id = AsyncMock(return_value=None)

        # Act & Assert
        with pytest.raises(NotFoundError, match="Role not found"):
            await authorization_service.assign_permissions_to_role(
                role_id=role_id,
                permission_ids=permission_ids
            )

    async def test_assign_role_to_user_success(
        self, authorization_service, mock_role_repository, sample_role
    ):
        """Test successful role assignment to user"""
        # Arrange
        user_id = 1
        assignment_data = UserRoleAssignmentCreateSchema(
            role_id=sample_role.id,
            expires_at=datetime.utcnow() + timedelta(days=30),
        )
        
        mock_role_repository.get_role_by_id = AsyncMock(return_value=sample_role)
        mock_role_repository.assign_role_to_user = AsyncMock(return_value=True)

        # Act
        result = await authorization_service.assign_role_to_user(
            user_id=user_id,
            assignment_data=assignment_data,
            assigned_by_user_id=2
        )

        # Assert
        assert result is True
        mock_role_repository.assign_role_to_user.assert_called_once()

    async def test_assign_nonexistent_role_to_user_raises_error(
        self, authorization_service, mock_role_repository
    ):
        """Test that assigning non-existent role to user raises NotFoundError"""
        # Arrange
        user_id = 1
        assignment_data = UserRoleAssignmentCreateSchema(role_id=999)
        mock_role_repository.get_role_by_id = AsyncMock(return_value=None)

        # Act & Assert
        with pytest.raises(NotFoundError, match="Role not found"):
            await authorization_service.assign_role_to_user(
                user_id=user_id,
                assignment_data=assignment_data
            )

    async def test_remove_role_from_user_success(
        self, authorization_service, mock_role_repository
    ):
        """Test successful role removal from user"""
        # Arrange
        user_id = 1
        role_id = 1
        mock_role_repository.remove_role_from_user = AsyncMock(return_value=True)

        # Act
        result = await authorization_service.remove_role_from_user(user_id, role_id)

        # Assert
        assert result is True
        mock_role_repository.remove_role_from_user.assert_called_once_with(user_id, role_id)

    async def test_get_user_permissions_superuser(
        self, authorization_service, mock_role_repository, sample_superuser
    ):
        """Test getting permissions for superuser returns all permissions"""
        # Arrange
        all_permissions = ["project.read", "project.write", "project.delete", "user.manage"]
        mock_role_repository.get_all_permission_strings = AsyncMock(return_value=all_permissions)

        # Act
        result = await authorization_service.get_user_permissions(sample_superuser.id)

        # Assert
        assert result == all_permissions
        mock_role_repository.get_all_permission_strings.assert_called_once()

    async def test_get_user_permissions_regular_user(
        self, authorization_service, mock_role_repository, sample_user
    ):
        """Test getting permissions for regular user"""
        # Arrange
        user_permissions = ["project.read"]
        mock_role_repository.get_user_permissions = AsyncMock(return_value=user_permissions)

        # Act
        result = await authorization_service.get_user_permissions(sample_user.id)

        # Assert
        assert result == user_permissions
        mock_role_repository.get_user_permissions.assert_called_once_with(sample_user.id)

    async def test_get_all_roles(self, authorization_service, mock_role_repository):
        """Test getting all roles"""
        # Arrange
        import datetime
        now = datetime.datetime.utcnow()
        roles = [
            UserRole(
                id=1, 
                name="Role 1", 
                is_active=True, 
                priority=100,
                is_system_role=False,
                created_at=now,
                updated_at=now,
                is_deleted=False
            ),
            UserRole(
                id=2, 
                name="Role 2", 
                is_active=True, 
                priority=200,
                is_system_role=False,
                created_at=now,
                updated_at=now,
                is_deleted=False
            ),
        ]
        mock_role_repository.get_active_roles = AsyncMock(return_value=roles)

        # Act
        result = await authorization_service.get_all_roles()

        # Assert
        assert len(result) == 2
        assert result[0].name == "Role 1"
        assert result[1].name == "Role 2"
        mock_role_repository.get_active_roles.assert_called_once()

    async def test_get_all_permissions(self, authorization_service, mock_role_repository):
        """Test getting all permissions"""
        # Arrange
        import datetime
        now = datetime.datetime.utcnow()
        permissions = [
            Permission(
                id=1, 
                name="Read", 
                resource="project", 
                action="read", 
                is_active=True,
                is_system_permission=False,
                created_at=now,
                updated_at=now,
                is_deleted=False
            ),
            Permission(
                id=2, 
                name="Write", 
                resource="project", 
                action="write", 
                is_active=True,
                is_system_permission=False,
                created_at=now,
                updated_at=now,
                is_deleted=False
            ),
        ]
        mock_role_repository.get_all_permissions = AsyncMock(return_value=permissions)

        # Act
        result = await authorization_service.get_all_permissions()

        # Assert
        assert len(result) == 2
        assert result[0].name == "Read"
        assert result[1].name == "Write"
        mock_role_repository.get_all_permissions.assert_called_once()

    async def test_get_role_permissions(
        self, authorization_service, mock_role_repository, sample_role
    ):
        """Test getting permissions for a specific role"""
        # Arrange
        permissions = ["project.read", "project.write"]
        mock_role_repository.get_role_by_id = AsyncMock(return_value=sample_role)
        mock_role_repository.get_role_permissions = AsyncMock(return_value=permissions)

        # Act
        result = await authorization_service.get_role_permissions(sample_role.id)

        # Assert
        assert result == permissions
        mock_role_repository.get_role_permissions.assert_called_once_with(sample_role.id)

    async def test_validate_permission_format_valid(self, authorization_service):
        """Test validation of valid permission format"""
        # Act & Assert - should not raise
        await authorization_service._validate_permission_format("project", "read")
        await authorization_service._validate_permission_format("user", "manage")
        await authorization_service._validate_permission_format("component", "create")

    async def test_validate_permission_format_invalid_resource(self, authorization_service):
        """Test validation with invalid resource format"""
        # Act & Assert
        with pytest.raises(ValidationError, match="Invalid resource format"):
            await authorization_service._validate_permission_format("", "read")
        
        with pytest.raises(ValidationError, match="Invalid resource format"):
            await authorization_service._validate_permission_format("invalid resource", "read")

    async def test_validate_permission_format_invalid_action(self, authorization_service):
        """Test validation with invalid action format"""
        # Act & Assert
        with pytest.raises(ValidationError, match="Invalid action format"):
            await authorization_service._validate_permission_format("project", "")
        
        with pytest.raises(ValidationError, match="Invalid action format"):
            await authorization_service._validate_permission_format("project", "invalid action")

    async def test_check_role_assignment_conflicts(
        self, authorization_service, mock_role_repository, sample_role
    ):
        """Test checking for role assignment conflicts"""
        # Arrange
        user_id = 1
        conflicting_roles = [
            UserRole(id=2, name="Conflicting Role", priority=50),
        ]
        mock_role_repository.get_user_active_roles = AsyncMock(return_value=conflicting_roles)

        # Act
        result = await authorization_service._check_role_assignment_conflicts(
            user_id, sample_role
        )

        # Assert - should identify conflicts based on priority or other business rules
        assert isinstance(result, bool)
        mock_role_repository.get_user_active_roles.assert_called_once_with(user_id)


class TestAuthorizationServiceIntegration:
    """Integration tests for AuthorizationService with real database interactions"""

    @pytest.mark.integration
    async def test_full_rbac_workflow(self, db_session, authorization_service):
        """Test complete RBAC workflow from role creation to permission checking"""
        # This would be an integration test that uses real database
        # and tests the complete workflow
        pass

    @pytest.mark.integration
    async def test_permission_inheritance(self, db_session, authorization_service):
        """Test permission inheritance through role hierarchy"""
        # Test hierarchical permission inheritance
        pass

    @pytest.mark.integration
    async def test_concurrent_role_assignments(self, db_session, authorization_service):
        """Test concurrent role assignments don't cause conflicts"""
        # Test concurrent access and race conditions
        pass


class TestAuthorizationServicePerformance:
    """Performance tests for AuthorizationService"""

    @pytest.mark.performance
    async def test_permission_check_performance(self, authorization_service):
        """Test permission check performance under load"""
        # Performance test for permission checking
        pass

    @pytest.mark.performance
    async def test_bulk_role_assignment_performance(self, authorization_service):
        """Test performance of bulk role assignments"""
        # Performance test for bulk operations
        pass