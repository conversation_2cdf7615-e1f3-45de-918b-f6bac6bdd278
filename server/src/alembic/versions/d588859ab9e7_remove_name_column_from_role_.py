

"""Remove name column from role_permissions table

Revision ID: d588859ab9e7
Revises: 71ba04a4132d
Create Date: 2025-08-09 19:51:31.368293

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd588859ab9e7'
down_revision = '71ba04a4132d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('role_permissions', 'name')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('role_permissions', sa.Column('name', sa.VARCHAR(length=255), autoincrement=False, nullable=False))
    # ### end Alembic commands ###
