"""Authorization Service.

This module provides business logic for role-based access control (RBAC) operations,
including role management, permission management, and authorization checks.
"""

import datetime
from typing import Dict, List, Optional, Set

from src.core.utils.datetime_utils import utcnow_naive

from sqlalchemy import and_, select
from sqlalchemy.ext.asyncio import AsyncSession
from src.config.logging_config import logger
from src.core.errors.exceptions import (
    BaseApplicationException,
    InvalidInputError,
    NotFoundError,
    UnauthorizedError,
)

# Unified systems imports
from src.core.errors.unified_error_handler import handle_service_errors
from src.core.monitoring.unified_performance_monitor import monitor_service_performance
from src.core.models.general.permission import Permission, RolePermission
from src.core.models.general.user import User
from src.core.models.general.user_role import UserRole, UserRoleAssignment
from src.core.repositories.general.role_repository import RoleRepository
from src.core.repositories.general.user_repository import UserRepository
from src.core.schemas.general.user_role_schemas import (
    RoleCreateSchema,
    RoleReadSchema,
    RoleUpdateSchema,
    UserRoleAssignmentCreateSchema,
    UserRoleAssignmentReadSchema,
)
from src.core.schemas.general.permission_schemas import (
    PermissionCreateSchema,
    PermissionReadSchema,
)
from src.core.utils.datetime_utils import utcnow_naive


class AuthorizationService:
    """Service for authorization and role-based access control operations."""

    def __init__(
        self,
        role_repository: RoleRepository,
        user_repository: UserRepository,
    ):
        """Initialize the authorization service."""
        self.role_repository = role_repository
        # Permission operations are handled through role_repository
        self.user_repository = user_repository
        logger.debug("AuthorizationService initialized")

    @handle_service_errors("authorization")
    @monitor_service_performance("authorization")
    async def create_role(
        self, role_data: RoleCreateSchema, created_by_user_id: Optional[int] = None
    ) -> RoleReadSchema:
        """Create a new role."""
        logger.info(f"Creating new role: {role_data.name}")

        # Check if role name already exists
        existing_role = await self.role_repository.get_by_name(role_data.name)
        if existing_role:
            raise InvalidInputError(f"Role with name '{role_data.name}' already exists")

        # Create role data dictionary
        role_dict = {
            "name": role_data.name,
            "description": role_data.description,
            "is_system_role": role_data.is_system_role if hasattr(role_data, 'is_system_role') else False,
            "is_active": True,
            "priority": role_data.priority if hasattr(role_data, 'priority') else 0,
        }

        # Save role
        saved_role = await self.role_repository.create(role_dict)
        logger.info(f"Role created successfully: {saved_role.name} (ID: {saved_role.id})")

        return RoleReadSchema.model_validate(saved_role)

    @handle_service_errors("authorization")
    @monitor_service_performance("authorization")
    async def get_role_by_id(self, role_id: int) -> RoleReadSchema:
        """Get role by ID with permissions loaded."""
        logger.debug(f"Retrieving role by ID: {role_id}")

        role = await self.role_repository.get_with_permissions(role_id)
        if not role:
            raise NotFoundError(code="ROLE_NOT_FOUND", detail=f"Role with ID {role_id} not found")

        return RoleReadSchema.model_validate(role)

    @handle_service_errors("authorization")
    @monitor_service_performance("authorization")
    async def get_all_roles(self) -> List[RoleReadSchema]:
        """Get all active roles."""
        logger.debug("Retrieving all active roles")

        roles = await self.role_repository.get_active_roles()
        return [RoleReadSchema.model_validate(role) for role in roles]

    @handle_service_errors("authorization")
    @monitor_service_performance("authorization")
    async def update_role(
        self, role_id: int, role_data: RoleUpdateSchema, updated_by_user_id: Optional[int] = None
    ) -> RoleReadSchema:
        """Update an existing role."""
        logger.info(f"Updating role ID: {role_id}")

        # Get existing role
        role = await self.role_repository.get_by_id(role_id)
        if not role:
            raise NotFoundError(code="ROLE_NOT_FOUND", detail=f"Role with ID {role_id} not found")

        # Check if name is being changed and if it conflicts
        if role_data.name and role_data.name != role.name:
            existing_role = await self.role_repository.get_by_name(role_data.name)
            if existing_role:
                raise InvalidInputError(f"Role with name '{role_data.name}' already exists")

        # Prepare update data
        update_data = role_data.model_dump(exclude_unset=True)
        update_data["updated_at"] = utcnow_naive()

        # Save changes
        updated_role = await self.role_repository.update(role_id, update_data)
        logger.info(f"Role updated successfully: {updated_role.name} (ID: {updated_role.id})")

        return RoleReadSchema.model_validate(updated_role)

    @handle_service_errors("authorization")
    @monitor_service_performance("authorization")
    async def delete_role(self, role_id: int) -> None:
        """Delete a role (soft delete)."""
        logger.info(f"Deleting role ID: {role_id}")

        # Get existing role
        role = await self.role_repository.get_by_id(role_id)
        if not role:
            raise NotFoundError(code="ROLE_NOT_FOUND", detail=f"Role with ID {role_id} not found")

        # Check if role is a system role
        if role.is_system_role:
            raise InvalidInputError("System roles cannot be deleted")

        # Prepare soft delete data
        delete_data = {
            "is_deleted": True,
            "is_active": False,
            "deleted_at": utcnow_naive()
        }

        # Save changes
        await self.role_repository.update(role_id, delete_data)
        logger.info(f"Role deleted successfully: {role.name} (ID: {role.id})")

    @handle_service_errors("authorization")
    @monitor_service_performance("authorization")
    async def create_permission(
        self, permission_data: PermissionCreateSchema, created_by_user_id: Optional[int] = None
    ) -> PermissionReadSchema:
        """Create a new permission."""
        logger.info(f"Creating new permission: {permission_data.name}")

        # Check if permission name already exists
        existing_permission = await self.role_repository.get_permission_by_name(permission_data.name)
        if existing_permission:
            raise InvalidInputError(f"Permission with name '{permission_data.name}' already exists")

        # Check if resource+action combination already exists
        existing_resource_action = await self.role_repository.get_permission_by_resource_and_action(
            permission_data.resource, permission_data.action
        )
        if existing_resource_action:
            raise InvalidInputError(
                f"Permission for resource '{permission_data.resource}' and action '{permission_data.action}' already exists"
            )

        # Create permission instance
        permission = Permission(
            name=permission_data.name,
            resource=permission_data.resource,
            action=permission_data.action,
            description=permission_data.description,
            is_system_permission=permission_data.is_system_permission if hasattr(permission_data, 'is_system_permission') else True,
            is_active=True,
        )

        # Save permission through role repository
        saved_permission = await self.role_repository.create_permission(permission)
        logger.info(f"Permission created successfully: {saved_permission.name} (ID: {saved_permission.id})")

        return PermissionReadSchema.model_validate(saved_permission)

    @handle_service_errors("authorization")
    @monitor_service_performance("authorization")
    async def get_all_permissions(self) -> List[PermissionReadSchema]:
        """Get all active permissions."""
        logger.debug("Retrieving all active permissions")

        permissions = await self.role_repository.get_all_permissions()
        return [PermissionReadSchema.model_validate(permission) for permission in permissions]

    @handle_service_errors("authorization")
    @monitor_service_performance("authorization")
    async def get_permission_by_id(self, permission_id: int) -> PermissionReadSchema:
        """Get permission by ID."""
        logger.debug(f"Retrieving permission by ID: {permission_id}")

        permission = await self.role_repository.get_permission_by_id(permission_id)
        if not permission:
            raise NotFoundError(code="PERMISSION_NOT_FOUND", detail=f"Permission with ID {permission_id} not found")

        return PermissionReadSchema.model_validate(permission)

    @handle_service_errors("authorization")
    @monitor_service_performance("authorization")
    async def get_role_permissions(self, role_id: int) -> List[str]:
        """Get all permissions for a role as a list of 'resource.action' strings."""
        logger.debug(f"Retrieving permissions for role {role_id}")

        # Validate role exists
        role = await self.role_repository.get_by_id(role_id)
        if not role:
            raise NotFoundError(code="ROLE_NOT_FOUND", detail=f"Role with ID {role_id} not found")

        # Get role permissions
        role_permissions = await self.role_repository.get_role_permissions(role_id)
        permissions = [f"{rp.permission.resource}.{rp.permission.action}" for rp in role_permissions if rp.permission.is_active]
        
        logger.debug(f"Role {role_id} has {len(permissions)} permissions")
        return permissions

    @handle_service_errors("authorization")
    @monitor_service_performance("authorization")
    async def get_user_roles(self, user_id: int) -> List[RoleReadSchema]:
        """Get all active roles assigned to a user."""
        logger.debug(f"Retrieving roles for user {user_id}")

        # Get user to validate it exists
        user = await self.user_repository.get_by_id(user_id)
        if not user:
            raise NotFoundError(code="USER_NOT_FOUND", detail=f"User with ID {user_id} not found")

        # Query role assignments directly to avoid relationship loading issues
        from src.core.models.general.user_role import UserRoleAssignment
        
        stmt = select(UserRoleAssignment).where(
            and_(
                UserRoleAssignment.user_id == user_id,
                UserRoleAssignment.is_active == True,
                UserRoleAssignment.is_deleted == False,
            )
        )
        result = await self.role_repository.db_session.execute(stmt)
        assignments = list(result.scalars().all())

        # Get roles from assignments
        roles = []
        for assignment in assignments:
            role = await self.role_repository.get_by_id(assignment.role_id)
            if role and role.is_active and not role.is_deleted:
                roles.append(RoleReadSchema.model_validate(role))

        logger.debug(f"User {user_id} has {len(roles)} active roles")
        return roles

    @handle_service_errors("authorization")
    @monitor_service_performance("authorization")
    async def assign_permissions_to_role(
        self, role_id: int, permission_ids: List[int], granted_by_user_id: Optional[int] = None
    ) -> RoleReadSchema:
        """Assign permissions to a role."""
        logger.info(f"Assigning permissions {permission_ids} to role {role_id}")

        # Validate role exists
        role = await self.role_repository.get_by_id(role_id)
        if not role:
            raise NotFoundError(code="ROLE_NOT_FOUND", detail=f"Role with ID {role_id} not found")

        # Validate all permissions exist
        for permission_id in permission_ids:
            permission = await self.role_repository.get_permission_by_id(permission_id)
            if not permission:
                raise NotFoundError(code="PERMISSION_NOT_FOUND", detail=f"Permission with ID {permission_id} not found")

        # Assign permissions to role
        success = await self.role_repository.assign_permissions_to_role(
            role_id, permission_ids, granted_by_user_id
        )

        if not success:
            raise BaseApplicationException("Failed to assign permissions to role")

        # Return updated role with permissions
        updated_role = await self.role_repository.get_with_permissions(role_id)
        logger.info(f"Permissions assigned successfully to role {role_id}")

        return RoleReadSchema.model_validate(updated_role)

    @handle_service_errors("authorization")
    @monitor_service_performance("authorization")
    async def assign_role_to_user(
        self, 
        user_id: int, 
        role_id: int, 
        assigned_by_user_id: Optional[int] = None,
        expires_at: Optional[datetime.datetime] = None,
        assignment_context: Optional[str] = None
    ) -> UserRoleAssignmentReadSchema:
        """Assign a role to a user."""
        logger.info(f"Assigning role {role_id} to user {user_id}")

        # Validate user exists
        user = await self.user_repository.get_by_id(user_id)
        if not user:
            raise NotFoundError(code="USER_NOT_FOUND", detail=f"User with ID {user_id} not found")

        # Validate role exists
        role = await self.role_repository.get_by_id(role_id)
        if not role:
            raise NotFoundError(code="ROLE_NOT_FOUND", detail=f"Role with ID {role_id} not found")

        # Check if assignment already exists and is active
        existing_assignments = [
            assignment for assignment in user.role_assignments
            if assignment.role_id == role_id and assignment.is_active and not assignment.is_deleted
        ]

        if existing_assignments:
            raise InvalidInputError(f"User {user_id} is already assigned role {role_id}")

        # Create role assignment
        assignment = UserRoleAssignment(
            user_id=user_id,
            role_id=role_id,
            assigned_by_user_id=assigned_by_user_id,
            assigned_at=utcnow_naive(),
            expires_at=expires_at,
            is_active=True,
            assignment_context=assignment_context,
        )

        # Save assignment using base repository create method
        await self.role_repository.db_session.merge(assignment)
        await self.role_repository.db_session.flush()

        logger.info(f"Role {role_id} assigned successfully to user {user_id}")
        return UserRoleAssignmentReadSchema.model_validate(assignment)

    @handle_service_errors("authorization")
    @monitor_service_performance("authorization")
    async def remove_role_from_user(
        self, user_id: int, role_id: int
    ) -> bool:
        """Remove a role from a user by deactivating the assignment."""
        logger.info(f"Removing role {role_id} from user {user_id}")

        # Get user with role assignments
        user = await self.user_repository.get_by_id(user_id)
        if not user:
            raise NotFoundError(code="USER_NOT_FOUND", detail=f"User with ID {user_id} not found")

        # Find active assignment
        active_assignment = None
        for assignment in user.role_assignments:
            if (
                assignment.role_id == role_id 
                and assignment.is_active 
                and not assignment.is_deleted
            ):
                active_assignment = assignment
                break

        if not active_assignment:
            raise NotFoundError(code="ASSIGNMENT_NOT_FOUND", detail=f"Active role assignment not found for user {user_id} and role {role_id}")

        # Deactivate assignment
        active_assignment.is_active = False
        await self.role_repository.db_session.flush()

        logger.info(f"Role {role_id} removed successfully from user {user_id}")
        return True

    @handle_service_errors("authorization")
    @monitor_service_performance("authorization")
    async def check_user_permission(
        self, user_id: int, resource: str, action: str
    ) -> bool:
        """Check if a user has permission to perform an action on a resource."""
        logger.debug(f"Checking permission for user {user_id}: {resource}.{action}")

        # Get user with role assignments
        user = await self.user_repository.get_by_id(user_id)
        if not user:
            logger.debug(f"User {user_id} not found")
            return False

        # If user is superuser, grant all permissions
        if user.is_superuser:
            logger.debug(f"User {user_id} is superuser, granting permission")
            return True

        # Query role assignments directly to avoid relationship loading issues
        from src.core.models.general.user_role import UserRoleAssignment
        
        stmt = select(UserRoleAssignment).where(
            and_(
                UserRoleAssignment.user_id == user_id,
                UserRoleAssignment.is_active == True,
                UserRoleAssignment.is_deleted == False,
            )
        )
        result = await self.role_repository.db_session.execute(stmt)
        active_assignments = list(result.scalars().all())

        if not active_assignments:
            logger.debug(f"User {user_id} has no active role assignments")
            return False

        # Check permissions for each role
        for assignment in active_assignments:
            role_permissions = await self.role_repository.get_role_permissions(assignment.role_id)
            
            for role_permission in role_permissions:
                permission = role_permission.permission
                if (
                    permission.resource == resource 
                    and permission.action == action
                    and permission.is_active
                ):
                    logger.debug(f"User {user_id} has permission {resource}.{action} via role {assignment.role_id}")
                    return True

        logger.debug(f"User {user_id} does not have permission {resource}.{action}")
        return False

    @handle_service_errors("authorization")
    @monitor_service_performance("authorization")
    async def get_user_permissions(self, user_id: int) -> Set[str]:
        """Get all permissions for a user as a set of 'resource.action' strings."""
        logger.debug(f"Retrieving all permissions for user {user_id}")

        # Get user with role assignments
        user = await self.user_repository.get_by_id(user_id)
        if not user:
            return set()

        permissions: Set[str] = set()

        # If user is superuser, they have all permissions
        if user.is_superuser:
            all_permissions = await self.role_repository.get_all_permissions()
            permissions.update(f"{p.resource}.{p.action}" for p in all_permissions)
            logger.debug(f"User {user_id} is superuser, granted all {len(permissions)} permissions")
            return permissions

        # Query role assignments directly to avoid relationship loading issues
        from src.core.models.general.user_role import UserRoleAssignment
        
        stmt = select(UserRoleAssignment).where(
            and_(
                UserRoleAssignment.user_id == user_id,
                UserRoleAssignment.is_active == True,
                UserRoleAssignment.is_deleted == False,
            )
        )
        result = await self.role_repository.db_session.execute(stmt)
        active_assignments = list(result.scalars().all())

        for assignment in active_assignments:
            role_permissions = await self.role_repository.get_role_permissions(assignment.role_id)
            
            for role_permission in role_permissions:
                permission = role_permission.permission
                if permission.is_active:
                    permissions.add(f"{permission.resource}.{permission.action}")

        logger.debug(f"User {user_id} has {len(permissions)} permissions")
        return permissions