"""Pydantic schemas for Permission and RolePermission models.

Request/response schemas for the permission system,
including permissions, role permissions, and related operations.
"""

import datetime
from typing import List, Optional

from pydantic import BaseModel, Field, field_validator
from src.core.schemas.base_schemas import BaseSchema


class PermissionBase(BaseModel):
    """Base schema for Permission with common fields."""

    name: str = Field(..., min_length=1, max_length=100, description="Permission name")
    resource: str = Field(..., min_length=1, max_length=100, description="Resource this permission applies to")
    action: str = Field(..., min_length=1, max_length=50, description="Action this permission allows")
    description: Optional[str] = Field(None, max_length=1000, description="Permission description")
    is_system_permission: bool = Field(True, description="Whether this is a system-defined permission")
    is_active: bool = Field(True, description="Whether the permission is active")

    @field_validator("name")
    @classmethod
    def validate_name(cls, v: str) -> str:
        """Validate and clean the permission name."""
        if not v or not v.strip():
            raise ValueError("Permission name cannot be empty or just whitespace")
        name = v.strip()
        if len(name) > 100:
            raise ValueError("Permission name cannot exceed 100 characters")
        return name

    @field_validator("resource")
    @classmethod
    def validate_resource(cls, v: str) -> str:
        """Validate and clean the resource name."""
        if not v or not v.strip():
            raise ValueError("Resource cannot be empty or just whitespace")
        resource = v.strip().lower()
        if len(resource) > 100:
            raise ValueError("Resource cannot exceed 100 characters")
        return resource

    @field_validator("action")
    @classmethod
    def validate_action(cls, v: str) -> str:
        """Validate and clean the action name."""
        if not v or not v.strip():
            raise ValueError("Action cannot be empty or just whitespace")
        action = v.strip().lower()
        if len(action) > 50:
            raise ValueError("Action cannot exceed 50 characters")
        return action

    @field_validator("description")
    @classmethod
    def validate_description(cls, v: Optional[str]) -> Optional[str]:
        """Validate and normalize description."""
        if v is not None:
            description = v.strip()
            if len(description) > 1000:
                raise ValueError("Description cannot exceed 1000 characters")
            return description if description else None
        return v


class PermissionCreate(PermissionBase):
    """Schema for creating a new permission."""

    pass


class PermissionUpdate(BaseModel):
    """Schema for updating an existing permission."""

    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=1000)
    is_active: Optional[bool] = None

    @field_validator("name")
    @classmethod
    def validate_name(cls, v: Optional[str]) -> Optional[str]:
        """Validate and clean the permission name for updates."""
        if v is not None:
            if not v.strip():
                raise ValueError("Permission name cannot be empty or just whitespace")
            name = v.strip()
            if len(name) > 100:
                raise ValueError("Permission name cannot exceed 100 characters")
            return name
        return v

    @field_validator("description")
    @classmethod
    def validate_description(cls, v: Optional[str]) -> Optional[str]:
        """Validate and normalize description for updates."""
        if v is not None:
            description = v.strip()
            if len(description) > 1000:
                raise ValueError("Description cannot exceed 1000 characters")
            return description if description else None
        return v


class PermissionResponse(BaseSchema):
    """Schema for permission responses."""

    id: int
    name: str
    resource: str
    action: str
    description: Optional[str]
    is_system_permission: bool
    is_active: bool
    created_at: datetime.datetime
    updated_at: datetime.datetime
    is_deleted: bool
    deleted_at: Optional[datetime.datetime]
    deleted_by_user_id: Optional[int]

    class Config:
        """Pydantic configuration for PermissionResponse."""

        from_attributes = True


class RolePermissionBase(BaseModel):
    """Base schema for RolePermission with common fields."""

    role_id: int = Field(..., description="ID of the role")
    permission_id: int = Field(..., description="ID of the permission")
    granted_by_user_id: Optional[int] = Field(None, description="ID of user who granted the permission")
    is_active: bool = Field(True, description="Whether the permission grant is active")
    grant_context: Optional[str] = Field(None, max_length=255, description="Context for granting the permission")

    @field_validator("grant_context")
    @classmethod
    def validate_grant_context(cls, v: Optional[str]) -> Optional[str]:
        """Validate and normalize grant context."""
        if v is not None:
            context = v.strip()
            if len(context) > 255:
                raise ValueError("Grant context cannot exceed 255 characters")
            return context if context else None
        return v


class RolePermissionCreate(RolePermissionBase):
    """Schema for creating a new role permission."""

    pass


class RolePermissionResponse(BaseSchema):
    """Schema for role permission responses."""

    id: int
    name: str
    role_id: int
    permission_id: int
    granted_by_user_id: Optional[int]
    granted_at: datetime.datetime
    is_active: bool
    grant_context: Optional[str]
    created_at: datetime.datetime
    updated_at: datetime.datetime
    is_deleted: bool
    deleted_at: Optional[datetime.datetime]
    deleted_by_user_id: Optional[int]

    class Config:
        """Pydantic configuration for RolePermissionResponse."""

        from_attributes = True


class PermissionsByResource(BaseModel):
    """Schema for grouping permissions by resource."""

    resource: str
    permissions: List[PermissionResponse]

    class Config:
        """Pydantic configuration for PermissionsByResource."""

        from_attributes = True


class UserPermissionsSummary(BaseModel):
    """Schema for user permissions summary."""

    user_id: int
    permissions: List[str]
    roles: List[str]
    is_superuser: bool

    class Config:
        """Pydantic configuration for UserPermissionsSummary."""

        from_attributes = True


# Schema aliases for authorization service
PermissionCreateSchema = PermissionCreate
PermissionReadSchema = PermissionResponse
PermissionUpdateSchema = PermissionUpdate
RolePermissionCreateSchema = RolePermissionCreate
RolePermissionReadSchema = RolePermissionResponse