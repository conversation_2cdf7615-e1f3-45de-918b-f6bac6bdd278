"""Permission Repository.

This module provides data access layer for Permission entities, extending the base
repository with permission-specific query methods and operations for RBAC authorization system.
"""

from typing import List, Optional

from sqlalchemy import and_, select
from sqlalchemy.ext.asyncio import AsyncSession

from src.config.logging_config import logger

# Unified systems imports
from src.core.errors.unified_error_handler import handle_repository_errors
from src.core.models.general.permission import Permission
from src.core.monitoring.unified_performance_monitor import (
    monitor_repository_performance,
)
from src.core.repositories.base_repository import BaseRepository


class PermissionRepository(BaseRepository[Permission]):
    """Repository for Permission entity data access operations."""

    def __init__(self, db_session: AsyncSession):
        """Initialize the Permission repository."""
        super().__init__(db_session, Permission)
        logger.debug("PermissionRepository initialized")

    @handle_repository_errors("permission")
    @monitor_repository_performance("permission")
    async def get_by_name(self, name: str) -> Optional[Permission]:
        """Get permission by name."""
        logger.debug(f"Retrieving permission by name: {name}")
        stmt = select(self.model).where(
            and_(
                self.model.name == name,
                self.model.is_active == True,
                self.model.is_deleted == False,
            )
        )
        result = await self.db_session.execute(stmt)
        permission = result.scalar_one_or_none()
        if permission:
            logger.debug(f"Permission found for name: {name}")
        else:
            logger.debug(f"No permission found for name: {name}")
        return permission

    @handle_repository_errors("permission")
    @monitor_repository_performance("permission")
    async def get_by_resource_and_action(
        self, resource: str, action: str
    ) -> Optional[Permission]:
        """Get permission by resource and action."""
        logger.debug(f"Retrieving permission by resource '{resource}' and action '{action}'")
        stmt = select(self.model).where(
            and_(
                self.model.resource == resource,
                self.model.action == action,
                self.model.is_active == True,
                self.model.is_deleted == False,
            )
        )
        result = await self.db_session.execute(stmt)
        permission = result.scalar_one_or_none()
        if permission:
            logger.debug(f"Permission found for resource '{resource}' and action '{action}'")
        else:
            logger.debug(f"No permission found for resource '{resource}' and action '{action}'")
        return permission

    @handle_repository_errors("permission")
    @monitor_repository_performance("permission")
    async def get_active_permissions(self) -> List[Permission]:
        """Get all active permissions."""
        logger.debug("Retrieving all active permissions")
        stmt = select(self.model).where(
            and_(
                self.model.is_active == True,
                self.model.is_deleted == False,
            )
        )
        result = await self.db_session.execute(stmt)
        permissions = list(result.scalars().all())
        logger.debug(f"Found {len(permissions)} active permissions")
        return permissions

    @handle_repository_errors("permission")
    @monitor_repository_performance("permission")
    async def get_system_permissions(self) -> List[Permission]:
        """Get all system-defined permissions."""
        logger.debug("Retrieving system permissions")
        stmt = select(self.model).where(
            and_(
                self.model.is_system_permission == True,
                self.model.is_active == True,
                self.model.is_deleted == False,
            )
        )
        result = await self.db_session.execute(stmt)
        permissions = list(result.scalars().all())
        logger.debug(f"Found {len(permissions)} system permissions")
        return permissions

    @handle_repository_errors("permission")
    @monitor_repository_performance("permission")
    async def get_permissions_by_resource(self, resource: str) -> List[Permission]:
        """Get all permissions for a specific resource."""
        logger.debug(f"Retrieving permissions for resource: {resource}")
        stmt = select(self.model).where(
            and_(
                self.model.resource == resource,
                self.model.is_active == True,
                self.model.is_deleted == False,
            )
        )
        result = await self.db_session.execute(stmt)
        permissions = list(result.scalars().all())
        logger.debug(f"Found {len(permissions)} permissions for resource '{resource}'")
        return permissions

    @handle_repository_errors("permission")
    @monitor_repository_performance("permission")
    async def get_permissions_by_action(self, action: str) -> List[Permission]:
        """Get all permissions for a specific action."""
        logger.debug(f"Retrieving permissions for action: {action}")
        stmt = select(self.model).where(
            and_(
                self.model.action == action,
                self.model.is_active == True,
                self.model.is_deleted == False,
            )
        )
        result = await self.db_session.execute(stmt)
        permissions = list(result.scalars().all())
        logger.debug(f"Found {len(permissions)} permissions for action '{action}'")
        return permissions