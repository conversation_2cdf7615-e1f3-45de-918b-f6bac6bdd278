"""Role Repository.

This module provides data access layer for UserRole and RolePermission entities, extending the base
repository with role-specific query methods and operations for RBAC authorization system.
"""

from typing import List, Optional

from sqlalchemy import and_, select, delete
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from src.config.logging_config import logger

# Unified systems imports
from src.core.errors.unified_error_handler import handle_repository_errors
from src.core.models.general.user_role import UserRole, UserRoleAssignment
from src.core.models.general.permission import RolePermission
from src.core.monitoring.unified_performance_monitor import (
    monitor_repository_performance,
)
from src.core.repositories.base_repository import BaseRepository


class RoleRepository(BaseRepository[UserRole]):
    """Repository for UserRole entity data access operations."""

    def __init__(self, db_session: AsyncSession):
        """Initialize the Role repository."""
        super().__init__(db_session, UserRole)
        logger.debug("RoleRepository initialized")

    @handle_repository_errors("role")
    @monitor_repository_performance("role")
    async def get_by_name(self, name: str) -> Optional[UserRole]:
        """Get role by name."""
        logger.debug(f"Retrieving role by name: {name}")
        stmt = select(self.model).where(
            and_(
                self.model.name == name,
                self.model.is_active == True,
                self.model.is_deleted == False,
            )
        )
        result = await self.db_session.execute(stmt)
        role = result.scalar_one_or_none()
        if role:
            logger.debug(f"Role found for name: {name}")
        else:
            logger.debug(f"No role found for name: {name}")
        return role

    @handle_repository_errors("role")
    @monitor_repository_performance("role")
    async def get_with_permissions(self, role_id: int) -> Optional[UserRole]:
        """Get role with its permissions loaded."""
        logger.debug(f"Retrieving role with permissions for ID: {role_id}")
        stmt = (
            select(self.model)
            .options(
                selectinload(self.model.role_permissions).selectinload(
                    RolePermission.permission
                )
            )
            .where(
                and_(
                    self.model.id == role_id,
                    self.model.is_active == True,
                    self.model.is_deleted == False,
                )
            )
        )
        result = await self.db_session.execute(stmt)
        role = result.scalar_one_or_none()
        if role:
            logger.debug(f"Role with permissions found for ID: {role_id}")
        else:
            logger.debug(f"No role found for ID: {role_id}")
        return role

    @handle_repository_errors("role")
    @monitor_repository_performance("role")
    async def get_active_roles(self) -> List[UserRole]:
        """Get all active roles."""
        logger.debug("Retrieving all active roles")
        stmt = select(self.model).where(
            and_(
                self.model.is_active == True,
                self.model.is_deleted == False,
            )
        )
        result = await self.db_session.execute(stmt)
        roles = list(result.scalars().all())
        logger.debug(f"Found {len(roles)} active roles")
        return roles

    @handle_repository_errors("role")
    @monitor_repository_performance("role")
    async def get_system_roles(self) -> List[UserRole]:
        """Get all system-defined roles."""
        logger.debug("Retrieving system roles")
        stmt = select(self.model).where(
            and_(
                self.model.is_system_role == True,
                self.model.is_active == True,
                self.model.is_deleted == False,
            )
        )
        result = await self.db_session.execute(stmt)
        roles = list(result.scalars().all())
        logger.debug(f"Found {len(roles)} system roles")
        return roles

    @handle_repository_errors("role")
    @monitor_repository_performance("role")
    async def assign_permissions_to_role(
        self, role_id: int, permission_ids: List[int], granted_by_user_id: Optional[int] = None
    ) -> bool:
        """Assign multiple permissions to a role."""
        logger.debug(f"Assigning permissions {permission_ids} to role {role_id}")
        
        # First, deactivate existing permissions for this role
        await self.revoke_permissions_from_role(role_id)
        
        # Create new role permission assignments
        role_permissions = []
        for permission_id in permission_ids:
            role_permission = RolePermission(
                role_id=role_id,
                permission_id=permission_id,
                granted_by_user_id=granted_by_user_id,
                is_active=True,
            )
            role_permissions.append(role_permission)
            
        if role_permissions:
            self.db_session.add_all(role_permissions)
            await self.db_session.flush()
            logger.debug(f"Assigned {len(role_permissions)} permissions to role {role_id}")
        
        return True

    @handle_repository_errors("role")
    @monitor_repository_performance("role")
    async def revoke_permissions_from_role(self, role_id: int) -> bool:
        """Revoke all permissions from a role by deactivating them."""
        logger.debug(f"Revoking permissions from role {role_id}")
        
        stmt = (
            select(RolePermission)
            .where(
                and_(
                    RolePermission.role_id == role_id,
                    RolePermission.is_active == True,
                    RolePermission.is_deleted == False,
                )
            )
        )
        result = await self.db_session.execute(stmt)
        role_permissions = list(result.scalars().all())
        
        for role_permission in role_permissions:
            role_permission.is_active = False
        
        if role_permissions:
            await self.db_session.flush()
            logger.debug(f"Revoked {len(role_permissions)} permissions from role {role_id}")
        
        return True

    @handle_repository_errors("role")
    @monitor_repository_performance("role")
    async def get_role_permissions(self, role_id: int) -> List[RolePermission]:
        """Get all active permissions for a role."""
        logger.debug(f"Retrieving permissions for role {role_id}")
        stmt = (
            select(RolePermission)
            .options(selectinload(RolePermission.permission))
            .where(
                and_(
                    RolePermission.role_id == role_id,
                    RolePermission.is_active == True,
                    RolePermission.is_deleted == False,
                )
            )
        )
        result = await self.db_session.execute(stmt)
        permissions = list(result.scalars().all())
        logger.debug(f"Found {len(permissions)} permissions for role {role_id}")
        return permissions

    # Permission management methods
    @handle_repository_errors("permission")
    @monitor_repository_performance("permission")
    async def get_all_permissions(self):
        """Get all active permissions."""
        from src.core.models.general.permission import Permission
        logger.debug("Retrieving all active permissions")
        stmt = select(Permission).where(
            and_(
                Permission.is_active == True,
                Permission.is_deleted == False,
            )
        )
        result = await self.db_session.execute(stmt)
        permissions = list(result.scalars().all())
        logger.debug(f"Found {len(permissions)} active permissions")
        return permissions

    @handle_repository_errors("permission")
    @monitor_repository_performance("permission")
    async def get_permission_by_id(self, permission_id: int):
        """Get permission by ID."""
        from src.core.models.general.permission import Permission
        logger.debug(f"Retrieving permission by ID: {permission_id}")
        stmt = select(Permission).where(
            and_(
                Permission.id == permission_id,
                Permission.is_active == True,
                Permission.is_deleted == False,
            )
        )
        result = await self.db_session.execute(stmt)
        permission = result.scalar_one_or_none()
        if permission:
            logger.debug(f"Permission found for ID: {permission_id}")
        else:
            logger.debug(f"No permission found for ID: {permission_id}")
        return permission

    @handle_repository_errors("permission")
    @monitor_repository_performance("permission")
    async def get_permission_by_name(self, name: str):
        """Get permission by name."""
        from src.core.models.general.permission import Permission
        logger.debug(f"Retrieving permission by name: {name}")
        stmt = select(Permission).where(
            and_(
                Permission.name == name,
                Permission.is_active == True,
                Permission.is_deleted == False,
            )
        )
        result = await self.db_session.execute(stmt)
        permission = result.scalar_one_or_none()
        if permission:
            logger.debug(f"Permission found for name: {name}")
        else:
            logger.debug(f"No permission found for name: {name}")
        return permission

    @handle_repository_errors("permission")
    @monitor_repository_performance("permission")
    async def get_permission_by_resource_and_action(self, resource: str, action: str):
        """Get permission by resource and action."""
        from src.core.models.general.permission import Permission
        logger.debug(f"Retrieving permission by resource/action: {resource}.{action}")
        stmt = select(Permission).where(
            and_(
                Permission.resource == resource,
                Permission.action == action,
                Permission.is_active == True,
                Permission.is_deleted == False,
            )
        )
        result = await self.db_session.execute(stmt)
        permission = result.scalar_one_or_none()
        if permission:
            logger.debug(f"Permission found for {resource}.{action}")
        else:
            logger.debug(f"No permission found for {resource}.{action}")
        return permission

    @handle_repository_errors("permission")
    @monitor_repository_performance("permission")
    async def create_permission(self, permission):
        """Create a new permission."""
        logger.debug(f"Creating permission: {permission.name}")
        self.db_session.add(permission)
        await self.db_session.flush()
        await self.db_session.refresh(permission)
        logger.debug(f"Permission created: {permission.name} (ID: {permission.id})")
        return permission