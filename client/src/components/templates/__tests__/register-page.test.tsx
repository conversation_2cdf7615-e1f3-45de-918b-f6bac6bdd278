/**
 * @file RegisterPage template tests
 * @description TDD tests for registration page component
 */
import type { RegisterPageProps } from "../register-page"

import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { render, screen } from "@testing-library/react"
import userEvent from "@testing-library/user-event"
import { beforeEach, describe, expect, it, vi } from "vitest"

import { RegisterPage } from "../register-page"

// Mock auth hooks
const mockRegisterMutation = {
  mutate: vi.fn(),
  isPending: false,
  error: null as any,
  isSuccess: false,
}

const mockAuthStore = {
  isLoading: false,
  setLoading: vi.fn(),
}

vi.mock("@/hooks/api/useAuth", () => ({
  useRegister: () => mockRegisterMutation,
}))

vi.mock("@/stores/authStore", () => ({
  useAuthStore: () => mockAuthStore,
}))

// Test wrapper with React Query
const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = createTestQueryClient()
  return (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
}

const defaultProps: RegisterPageProps = {
  onSuccess: vi.fn(),
  onError: vi.fn(),
}

describe("RegisterPage", () => {
  let user: ReturnType<typeof userEvent.setup>

  beforeEach(() => {
    user = userEvent.setup()
    vi.clearAllMocks()
    // Reset mock state
    mockRegisterMutation.mutate.mockClear()
    mockRegisterMutation.isPending = false
    mockRegisterMutation.error = null
    mockRegisterMutation.isSuccess = false
    mockAuthStore.isLoading = false
  })

  // Rendering Tests
  describe("Rendering", () => {
    it("renders with correct layout and title", () => {
      render(
        <TestWrapper>
          <RegisterPage {...defaultProps} />
        </TestWrapper>
      )

      // Check AuthLayout integration
      expect(screen.getByRole("main")).toBeInTheDocument()
      expect(screen.getByTestId("auth-container")).toBeInTheDocument()
      expect(screen.getByTestId("brand-logo")).toBeInTheDocument()

      // Check page title
      expect(
        screen.getByRole("heading", { name: /create your account/i })
      ).toBeInTheDocument()
    })

    it("renders registration form with all required elements", () => {
      render(
        <TestWrapper>
          <RegisterPage {...defaultProps} />
        </TestWrapper>
      )

      // Personal information fields
      expect(screen.getByLabelText(/full name/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/email address/i)).toBeInTheDocument()
      expect(
        screen.getByPlaceholderText(/enter your password/i)
      ).toBeInTheDocument()
      expect(
        screen.getByPlaceholderText(/confirm your password/i)
      ).toBeInTheDocument()

      // Submit button
      expect(
        screen.getByRole("button", { name: /create account/i })
      ).toBeInTheDocument()

      // Sign in link
      expect(screen.getByText(/already have an account/i)).toBeInTheDocument()
    })

    it("renders professional credentials section when enabled", () => {
      render(
        <TestWrapper>
          <RegisterPage {...defaultProps} showProfessionalFields={true} />
        </TestWrapper>
      )

      // Professional credentials section
      expect(screen.getByText(/professional credentials/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/professional license/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/company name/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/job title/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/years of experience/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/specializations/i)).toBeInTheDocument()
    })

    it("renders with custom title when provided", () => {
      render(
        <TestWrapper>
          <RegisterPage {...defaultProps} title="Join Us" />
        </TestWrapper>
      )

      expect(
        screen.getByRole("heading", { name: /join us/i })
      ).toBeInTheDocument()
    })

    it("renders with custom description when provided", () => {
      render(
        <TestWrapper>
          <RegisterPage
            {...defaultProps}
            description="Create your professional account"
          />
        </TestWrapper>
      )

      expect(
        screen.getByText(/create your professional account/i)
      ).toBeInTheDocument()
    })

    it("hides optional links when disabled", () => {
      render(
        <TestWrapper>
          <RegisterPage {...defaultProps} showSignInLink={false} />
        </TestWrapper>
      )

      expect(
        screen.queryByText(/already have an account/i)
      ).not.toBeInTheDocument()
    })

    it("applies custom className correctly", () => {
      render(
        <TestWrapper>
          <RegisterPage {...defaultProps} className="custom-register-page" />
        </TestWrapper>
      )

      const authContainer = screen.getByTestId("auth-container")
      expect(authContainer).toHaveClass("custom-register-page")
    })
  })

  // Form Interaction Tests
  describe("Form Interaction", () => {
    it("handles successful registration submission with minimal data", async () => {
      const mockOnSuccess = vi.fn()

      render(
        <TestWrapper>
          <RegisterPage {...defaultProps} onSuccess={mockOnSuccess} />
        </TestWrapper>
      )

      // Fill required fields
      await user.type(screen.getByLabelText(/full name/i), "John Doe")
      await user.type(
        screen.getByLabelText(/email address/i),
        "<EMAIL>"
      )
      await user.type(
        screen.getByPlaceholderText(/enter your password/i),
        "SecurePass123!"
      )
      await user.type(
        screen.getByPlaceholderText(/confirm your password/i),
        "SecurePass123!"
      )

      // Submit
      const submitButton = screen.getByRole("button", {
        name: /create account/i,
      })
      await user.click(submitButton)

      expect(mockRegisterMutation.mutate).toHaveBeenCalledWith({
        name: "John Doe",
        email: "<EMAIL>",
        password: "SecurePass123!",
        confirm_password: "SecurePass123!",
      })
    })

    it("handles successful registration with professional credentials", async () => {
      render(
        <TestWrapper>
          <RegisterPage {...defaultProps} showProfessionalFields={true} />
        </TestWrapper>
      )

      // Fill all fields
      await user.type(screen.getByLabelText(/full name/i), "Jane Engineer")
      await user.type(
        screen.getByLabelText(/email address/i),
        "<EMAIL>"
      )
      await user.type(
        screen.getByPlaceholderText(/enter your password/i),
        "SecurePass123!"
      )
      await user.type(
        screen.getByPlaceholderText(/confirm your password/i),
        "SecurePass123!"
      )

      // Professional fields
      await user.type(
        screen.getByLabelText(/professional license/i),
        "PE-12345"
      )
      await user.type(
        screen.getByLabelText(/company name/i),
        "Engineering Solutions"
      )
      await user.type(
        screen.getByLabelText(/job title/i),
        "Senior Electrical Engineer"
      )
      await user.type(screen.getByLabelText(/years of experience/i), "10")
      await user.type(
        screen.getByLabelText(/specializations/i),
        "Power Systems, Industrial Control"
      )

      const submitButton = screen.getByRole("button", {
        name: /create account/i,
      })
      await user.click(submitButton)

      expect(mockRegisterMutation.mutate).toHaveBeenCalledWith({
        name: "Jane Engineer",
        email: "<EMAIL>",
        password: "SecurePass123!",
        confirm_password: "SecurePass123!",
        professional_license: "PE-12345",
        company_name: "Engineering Solutions",
        job_title: "Senior Electrical Engineer",
        years_experience: 10,
        specializations: ["Power Systems", "Industrial Control"],
      })
    })

    it("validates required fields on submission", async () => {
      render(
        <TestWrapper>
          <RegisterPage {...defaultProps} />
        </TestWrapper>
      )

      const submitButton = screen.getByRole("button", {
        name: /create account/i,
      })
      await user.click(submitButton)

      // Should show validation errors
      expect(screen.getByText(/full name is required/i)).toBeInTheDocument()
      expect(
        screen.getByText(/email or username is required/i)
      ).toBeInTheDocument()
      expect(screen.getByText(/password is required/i)).toBeInTheDocument()

      expect(mockRegisterMutation.mutate).not.toHaveBeenCalled()
    })

    it("validates password confirmation matching", async () => {
      render(
        <TestWrapper>
          <RegisterPage {...defaultProps} />
        </TestWrapper>
      )

      await user.type(screen.getByLabelText(/full name/i), "Test User")
      await user.type(
        screen.getByLabelText(/email address/i),
        "<EMAIL>"
      )
      await user.type(
        screen.getByPlaceholderText(/enter your password/i),
        "SecurePass123!"
      )
      await user.type(
        screen.getByPlaceholderText(/confirm your password/i),
        "DifferentPass123!"
      )

      const submitButton = screen.getByRole("button", {
        name: /create account/i,
      })
      await user.click(submitButton)

      expect(screen.getByText(/passwords do not match/i)).toBeInTheDocument()
      expect(mockRegisterMutation.mutate).not.toHaveBeenCalled()
    })

    it("validates professional license format when provided", async () => {
      render(
        <TestWrapper>
          <RegisterPage {...defaultProps} showProfessionalFields={true} />
        </TestWrapper>
      )

      // Fill required fields
      await user.type(screen.getByLabelText(/full name/i), "Test User")
      await user.type(
        screen.getByLabelText(/email address/i),
        "<EMAIL>"
      )
      await user.type(
        screen.getByPlaceholderText(/enter your password/i),
        "SecurePass123!"
      )
      await user.type(
        screen.getByPlaceholderText(/confirm your password/i),
        "SecurePass123!"
      )

      // Invalid license format
      await user.type(screen.getByLabelText(/professional license/i), "INVALID")

      const submitButton = screen.getByRole("button", {
        name: /create account/i,
      })
      await user.click(submitButton)

      expect(screen.getByText(/invalid license format/i)).toBeInTheDocument()
    })
  })

  // Loading State Tests
  describe("Loading States", () => {
    it("shows loading state during registration", () => {
      mockRegisterMutation.isPending = true

      render(
        <TestWrapper>
          <RegisterPage {...defaultProps} />
        </TestWrapper>
      )

      const submitButton = screen.getByRole("button", {
        name: /creating account/i,
      })
      expect(submitButton).toBeDisabled()
      expect(submitButton).toHaveAttribute("aria-disabled", "true")
    })

    it("disables form fields during registration", () => {
      mockRegisterMutation.isPending = true

      render(
        <TestWrapper>
          <RegisterPage {...defaultProps} />
        </TestWrapper>
      )

      expect(screen.getByLabelText(/full name/i)).toBeDisabled()
      expect(screen.getByLabelText(/email address/i)).toBeDisabled()
      expect(screen.getByPlaceholderText(/enter your password/i)).toBeDisabled()
      expect(
        screen.getByPlaceholderText(/confirm your password/i)
      ).toBeDisabled()
    })
  })

  // Error Handling Tests
  describe("Error Handling", () => {
    it("displays server error messages", () => {
      const errorMessage = "Email already exists"
      mockRegisterMutation.error = { message: errorMessage }

      render(
        <TestWrapper>
          <RegisterPage {...defaultProps} />
        </TestWrapper>
      )

      expect(screen.getByText(errorMessage)).toBeInTheDocument()
      expect(screen.getByRole("alert")).toBeInTheDocument()
    })

    it("calls onError callback when error occurs", () => {
      const mockOnError = vi.fn()
      const error = { message: "Registration failed" }

      mockRegisterMutation.error = error

      render(
        <TestWrapper>
          <RegisterPage {...defaultProps} onError={mockOnError} />
        </TestWrapper>
      )

      expect(mockOnError).toHaveBeenCalledWith(error)
    })
  })

  // Success State Tests
  describe("Success States", () => {
    it("calls onSuccess callback when registration succeeds", () => {
      const mockOnSuccess = vi.fn()

      mockRegisterMutation.isSuccess = true

      render(
        <TestWrapper>
          <RegisterPage {...defaultProps} onSuccess={mockOnSuccess} />
        </TestWrapper>
      )

      expect(mockOnSuccess).toHaveBeenCalled()
    })
  })

  // Navigation Tests
  describe("Navigation", () => {
    it("handles sign in link click", async () => {
      const mockOnSignIn = vi.fn()

      render(
        <TestWrapper>
          <RegisterPage {...defaultProps} onSignIn={mockOnSignIn} />
        </TestWrapper>
      )

      const signInLink = screen.getByText(/sign in/i)
      await user.click(signInLink)

      expect(mockOnSignIn).toHaveBeenCalled()
    })
  })

  // Accessibility Tests
  describe("Accessibility", () => {
    it("has proper form structure with labels", () => {
      render(
        <TestWrapper>
          <RegisterPage {...defaultProps} />
        </TestWrapper>
      )

      const nameInput = screen.getByLabelText(/full name/i)
      const emailInput = screen.getByLabelText(/email address/i)
      const passwordInput = screen.getByPlaceholderText(/enter your password/i)
      const confirmPasswordInput = screen.getByPlaceholderText(
        /confirm your password/i
      )

      expect(nameInput).toHaveAttribute("type", "text")
      expect(nameInput).toHaveAttribute("required")
      expect(nameInput).toHaveAttribute("aria-invalid", "false")

      expect(emailInput).toHaveAttribute("type", "email")
      expect(emailInput).toHaveAttribute("required")

      expect(passwordInput).toHaveAttribute("type", "password")
      expect(passwordInput).toHaveAttribute("required")

      expect(confirmPasswordInput).toHaveAttribute("type", "password")
      expect(confirmPasswordInput).toHaveAttribute("required")
    })

    it("provides proper keyboard navigation", async () => {
      render(
        <TestWrapper>
          <RegisterPage {...defaultProps} />
        </TestWrapper>
      )

      const nameInput = screen.getByLabelText(/full name/i)
      const emailInput = screen.getByLabelText(/email address/i)
      const passwordInput = screen.getByPlaceholderText(/enter your password/i)
      const confirmPasswordInput = screen.getByPlaceholderText(
        /confirm your password/i
      )

      // Tab navigation through required fields
      nameInput.focus()
      expect(nameInput).toHaveFocus()

      await user.tab()
      expect(emailInput).toHaveFocus()

      await user.tab()
      expect(passwordInput).toHaveFocus()

      await user.tab() // Password visibility toggle
      await user.tab() // Confirm password
      expect(confirmPasswordInput).toHaveFocus()
    })

    it("announces validation errors to screen readers", async () => {
      render(
        <TestWrapper>
          <RegisterPage {...defaultProps} />
        </TestWrapper>
      )

      const submitButton = screen.getByRole("button", {
        name: /create account/i,
      })
      await user.click(submitButton)

      const errorElements = screen.getAllByRole("alert")
      expect(errorElements.length).toBeGreaterThanOrEqual(3) // At least name, email, and password errors
    })
  })

  // Integration Tests
  describe("Integration", () => {
    it("integrates properly with AuthLayout", () => {
      render(
        <TestWrapper>
          <RegisterPage {...defaultProps} />
        </TestWrapper>
      )

      // Verify AuthLayout structure
      expect(screen.getByRole("main")).toBeInTheDocument()
      expect(screen.getByTestId("auth-container")).toBeInTheDocument()
      expect(screen.getByTestId("brand-logo")).toBeInTheDocument()
    })

    it("works correctly with different props combinations", () => {
      render(
        <TestWrapper>
          <RegisterPage
            {...defaultProps}
            title="Professional Registration"
            description="Join our professional network"
            showProfessionalFields={true}
            showSignInLink={false}
            className="custom-class"
          />
        </TestWrapper>
      )

      expect(
        screen.getByRole("heading", { name: /professional registration/i })
      ).toBeInTheDocument()
      expect(
        screen.getByText(/join our professional network/i)
      ).toBeInTheDocument()
      expect(screen.getByText(/professional credentials/i)).toBeInTheDocument()
      expect(
        screen.queryByText(/already have an account/i)
      ).not.toBeInTheDocument()
    })

    it("handles complex professional registration workflow", async () => {
      render(
        <TestWrapper>
          <RegisterPage {...defaultProps} showProfessionalFields={true} />
        </TestWrapper>
      )

      // Comprehensive form fill
      await user.type(screen.getByLabelText(/full name/i), "Dr. Sarah Johnson")
      await user.type(
        screen.getByLabelText(/email address/i),
        "<EMAIL>"
      )
      await user.type(
        screen.getByPlaceholderText(/enter your password/i),
        "ComplexPass123!"
      )
      await user.type(
        screen.getByPlaceholderText(/confirm your password/i),
        "ComplexPass123!"
      )
      await user.type(
        screen.getByLabelText(/professional license/i),
        "PE-54321"
      )
      await user.type(
        screen.getByLabelText(/company name/i),
        "Power Engineering Solutions LLC"
      )
      await user.type(
        screen.getByLabelText(/job title/i),
        "Principal Electrical Engineer"
      )
      await user.type(screen.getByLabelText(/years of experience/i), "15")
      await user.type(
        screen.getByLabelText(/specializations/i),
        "Power Generation, Renewable Energy, Grid Integration"
      )

      const submitButton = screen.getByRole("button", {
        name: /create account/i,
      })
      await user.click(submitButton)

      expect(mockRegisterMutation.mutate).toHaveBeenCalledWith({
        name: "Dr. Sarah Johnson",
        email: "<EMAIL>",
        password: "ComplexPass123!",
        confirm_password: "ComplexPass123!",
        professional_license: "PE-54321",
        company_name: "Power Engineering Solutions LLC",
        job_title: "Principal Electrical Engineer",
        years_experience: 15,
        specializations: [
          "Power Generation",
          "Renewable Energy",
          "Grid Integration",
        ],
      })
    })
  })
})
