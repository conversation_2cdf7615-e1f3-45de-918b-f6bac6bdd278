/**
 * @file LoginForm organism component
 * @description Complete login form with validation, auth integration, and professional UI
 */

import * as React from "react"

import type {
  LoginFormData,
  ValidationErrors,
} from "@/components/modules/auth/types"

import { useAuthStore } from "@/stores/authStore"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"

import { cn } from "@/lib/utils"
import { loginFormSchema } from "@/lib/validation/forms"
import { useLogin } from "@/hooks/api/useAuth"

import { Button } from "@/components/atoms/button"
import { UnifiedIcon as Icon } from "@/components/atoms/icon"
import { InputField } from "@/components/molecules/input-field"
import { PasswordInput } from "@/components/molecules/password-input"

export interface LoginFormProps {
  /** Custom title for the form */
  title?: string
  /** Custom description for the form */
  description?: string
  /** Show forgot password link */
  showForgotPassword?: boolean
  /** Show sign up link */
  showSignUpLink?: boolean
  /** Call<PERSON> fired on successful login */
  onSuccess?: (response: any) => void
  /** Callback fired on login error */
  onError?: (error: any) => void
  /** Callback fired when forgot password link is clicked */
  onForgotPassword?: () => void
  /** Callback fired when sign up link is clicked */
  onSignUp?: () => void
  /** Additional CSS classes */
  className?: string
  /** Test identifier */
  "data-testid"?: string
}

export const LoginForm = React.forwardRef<HTMLFormElement, LoginFormProps>(
  (
    {
      title = "Welcome Back",
      description = "Sign in to your account to continue",
      showForgotPassword = true,
      showSignUpLink = true,
      onSuccess,
      onError,
      onForgotPassword,
      onSignUp,
      className,
      "data-testid": dataTestId,
    },
    ref
  ) => {
    // Auth hooks and state
    const login = useLogin()
    const { isLoading } = useAuthStore()

    // React Hook Form with Zod
    const form = useForm<LoginFormData>({
      resolver: zodResolver(loginFormSchema),
      defaultValues: { username: "", password: "" },
      mode: "onBlur",
    })

    const {
      register: formRegister,
      handleSubmit,
      formState: { errors, isSubmitting },
      setValue,
    } = form

    // Handle input changes to keep controlled components in sync if needed
    const handleInputChange = React.useCallback(
      (field: keyof LoginFormData) =>
        (event: React.ChangeEvent<HTMLInputElement>) => {
          const value = event.target.value
          setValue(field, value, { shouldValidate: true })
        },
      [setValue]
    )

    // Submit handler
    const onSubmit = React.useCallback(
      async (data: LoginFormData) => {
        // Submit with { username, password } to match API and MSW expectations
        login.mutate({
          username: data.username,
          password: data.password,
        } as any)
      },
      [login]
    )

    // Handle login success
    React.useEffect(() => {
      if (login.isSuccess && login.data) {
        onSuccess?.(login.data)
      }
    }, [login.isSuccess, login.data, onSuccess])

    // Handle login error
    React.useEffect(() => {
      if (login.error) {
        onError?.(login.error)
      }
    }, [login.error, onError])

    // Determine loading state
    const isPending = login.isPending || isLoading
    const hasServerError = Boolean(login.error)

    return (
      <form
        ref={ref}
        onSubmit={handleSubmit(onSubmit)}
        className={cn(
          "bg-card w-full max-w-md space-y-6 rounded-lg border p-6 shadow-sm",
          className
        )}
        data-testid={dataTestId}
        noValidate
      >
        {/* Header */}
        <div className="space-y-2 text-center">
          <h1 className="text-foreground text-2xl font-semibold tracking-tight">
            {title}
          </h1>
          {description && (
            <p className="text-muted-foreground text-sm">{description}</p>
          )}
        </div>

        {/* Server Error Display */}
        {hasServerError && (
          <div
            role="alert"
            className="border-destructive bg-destructive/10 rounded-md border p-3"
          >
            <div className="flex items-center space-x-2">
              <Icon type="alert" className="text-destructive h-4 w-4" />
              <p className="text-destructive text-sm">
                {login.error?.message || "An error occurred during login"}
              </p>
            </div>
          </div>
        )}

        {/* Form Fields */}
        <div className="space-y-4">
          {/* Email Field */}
          <InputField
            label="Email Address"
            type="email"
            {...formRegister("username")}
            error={errors.username?.message as string}
            required
            disabled={isPending || isSubmitting}
            placeholder="Enter your email address"
            autoComplete="email"
            className="w-full"
          />

          {/* Password Field */}
          <PasswordInput
            label="Password"
            {...formRegister("password")}
            error={errors.password?.message as string}
            required
            disabled={isPending || isSubmitting}
            placeholder="Enter your password"
            autoComplete="current-password"
            className="w-full"
          />
        </div>

        {/* Forgot Password Link */}
        {showForgotPassword && (
          <div className="flex justify-end">
            <button
              type="button"
              onClick={onForgotPassword}
              className="text-primary hover:text-primary/80 focus:ring-primary text-sm focus:ring-2 focus:ring-offset-2 focus:outline-none"
              disabled={isPending || isSubmitting}
            >
              Forgot password?
            </button>
          </div>
        )}

        {/* Submit Button */}
        <Button
          type="submit"
          className="w-full"
          disabled={isPending || isSubmitting}
          aria-disabled={isPending || isSubmitting}
        >
          {isPending ? (
            <>
              <Icon type="refresh" className="mr-2 h-4 w-4 animate-spin" />
              Signing in...
            </>
          ) : (
            <>
              <Icon type="login" className="mr-2 h-4 w-4" />
              Sign in
            </>
          )}
        </Button>

        {/* Sign Up Link */}
        {showSignUpLink && (
          <div className="text-muted-foreground text-center text-sm">
            Don&apos;t have an account?{" "}
            <button
              type="button"
              onClick={onSignUp}
              className="text-primary hover:text-primary/80 focus:ring-primary font-medium focus:ring-2 focus:ring-offset-2 focus:outline-none"
              disabled={isSubmitting}
            >
              Sign up
            </button>
          </div>
        )}
      </form>
    )
  }
)

LoginForm.displayName = "LoginForm"
