/**
 * Role Management Component
 * 
 * A comprehensive role management interface for RBAC operations
 */

import { useState } from "react"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { authorizationApi } from "@/lib/api/endpoints/authorization"
import type { Role, RoleCreate, RoleUpdate } from "@/lib/api/types/authorization"

export const RoleManagement = () => {
  const [activeTab, setActiveTab] = useState<"roles" | "hierarchy">("roles")
  const [editingRole, setEditingRole] = useState<Role | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const queryClient = useQueryClient()

  // Queries
  const { data: roles, error: rolesError, isLoading: rolesLoading } = useQuery({
    queryKey: ["roles"],
    queryFn: authorizationApi.getAllRoles,
  })

  const { data: roleHierarchy, error: hierarchyError } = useQuery({
    queryKey: ["roleHierarchy"],
    queryFn: authorizationApi.getRoleHierarchy,
    enabled: activeTab === "hierarchy",
  })

  // Mutations
  const createRoleMutation = useMutation({
    mutationFn: authorizationApi.createRole,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["roles"] })
      setIsCreating(false)
    },
  })

  const updateRoleMutation = useMutation({
    mutationFn: ({ id, data }: { id: number; data: RoleUpdate }) =>
      authorizationApi.updateRole(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["roles"] })
      setEditingRole(null)
    },
  })

  const deleteRoleMutation = useMutation({
    mutationFn: authorizationApi.deleteRole,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["roles"] })
    },
  })

  // Handlers
  const handleCreateRole = (roleData: RoleCreate) => {
    createRoleMutation.mutate(roleData)
  }

  const handleUpdateRole = (roleData: RoleUpdate) => {
    if (editingRole) {
      updateRoleMutation.mutate({ id: editingRole.id, data: roleData })
    }
  }

  const handleDeleteRole = (roleId: number) => {
    if (window.confirm("Are you sure you want to delete this role?")) {
      deleteRoleMutation.mutate(roleId)
    }
  }

  if (rolesError) {
    return (
      <div className="p-4">
        <div className="text-red-600">
          <h2 className="text-xl font-semibold">Error Loading Roles</h2>
          <p>{(rolesError as Error).message}</p>
          <button 
            className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            onClick={() => queryClient.invalidateQueries({ queryKey: ["roles"] })}
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Role Management</h1>
        <p className="text-gray-600">Manage user roles and permissions</p>
      </div>

      {/* Tab Navigation */}
      <div className="mb-6 border-b">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab("roles")}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === "roles"
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            }`}
          >
            Roles
          </button>
          <button
            onClick={() => setActiveTab("hierarchy")}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === "hierarchy"
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            }`}
          >
            Hierarchy
          </button>
        </nav>
      </div>

      {/* Roles Tab */}
      {activeTab === "roles" && (
        <div>
          {/* Create Role Button */}
          <div className="mb-4">
            <button
              onClick={() => setIsCreating(true)}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Create Role
            </button>
          </div>

          {/* Roles List */}
          {rolesLoading ? (
            <div>Loading roles...</div>
          ) : roles && roles.length > 0 ? (
            <div className="space-y-4">
              {roles.map((role) => (
                <div key={role.id} className="p-4 border rounded-lg">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="text-lg font-semibold">{role.name}</h3>
                      <p className="text-gray-600">{role.description}</p>
                      <div className="mt-2 text-sm text-gray-500">
                        <span>Priority: {role.priority}</span>
                        {role.is_system_role && (
                          <span className="ml-4 text-orange-600">System Role</span>
                        )}
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setEditingRole(role)}
                        className="px-3 py-1 text-blue-600 hover:bg-blue-50 rounded"
                      >
                        Edit
                      </button>
                      <button
                        onClick={() => handleDeleteRole(role.id)}
                        disabled={role.is_system_role}
                        className={`px-3 py-1 rounded ${
                          role.is_system_role
                            ? "text-gray-400 cursor-not-allowed"
                            : "text-red-600 hover:bg-red-50"
                        }`}
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500">No roles found</p>
            </div>
          )}

          {/* Pagination */}
          {roles && roles.length > 0 && (
            <div className="mt-6 flex justify-center">
              <div className="flex items-center space-x-4">
                <button className="px-3 py-1 text-gray-500 hover:text-gray-700">
                  Previous
                </button>
                <span>Page 1 of 3</span>
                <button className="px-3 py-1 text-gray-500 hover:text-gray-700">
                  Next
                </button>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Hierarchy Tab */}
      {activeTab === "hierarchy" && (
        <div>
          {hierarchyError ? (
            <div className="text-center py-8">
              <p className="text-gray-500">Loading role hierarchy...</p>
            </div>
          ) : (
            <div>
              <h2 className="text-xl font-semibold mb-4">Role Hierarchy</h2>
              {roleHierarchy && (
                <div className="space-y-2">
                  {roleHierarchy.map((role) => (
                    <div key={role.id} className="p-2 border rounded">
                      <div className="font-medium">{role.name}</div>
                      <div className="text-sm text-gray-600">{role.description}</div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Create Role Form */}
      {isCreating && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <div className="bg-white p-6 rounded-lg max-w-md w-full mx-4">
            <h2 className="text-lg font-semibold mb-4">Create Role</h2>
            <RoleForm
              onSubmit={handleCreateRole}
              onCancel={() => setIsCreating(false)}
            />
          </div>
        </div>
      )}

      {/* Edit Role Form */}
      {editingRole && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <div className="bg-white p-6 rounded-lg max-w-md w-full mx-4">
            <h2 className="text-lg font-semibold mb-4">Edit Role</h2>
            <RoleForm
              initialData={editingRole}
              onSubmit={handleUpdateRole}
              onCancel={() => setEditingRole(null)}
              isEdit
            />
          </div>
        </div>
      )}
    </div>
  )
}

// Role Form Component
interface RoleFormProps {
  initialData?: Role
  onSubmit: (data: RoleCreate | RoleUpdate) => void
  onCancel: () => void
  isEdit?: boolean
}

const RoleForm = ({ initialData, onSubmit, onCancel, isEdit }: RoleFormProps) => {
  const [formData, setFormData] = useState({
    name: initialData?.name || "",
    description: initialData?.description || "",
    permissions: initialData?.permissions || '["read"]',
    priority: initialData?.priority || 0,
    notes: initialData?.notes || "",
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit({
      name: formData.name,
      description: formData.description,
      is_system_role: false,
      is_active: true,
      permissions: formData.permissions,
      parent_role_id: undefined,
      priority: formData.priority,
      notes: formData.notes,
    })
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label htmlFor="name" className="block text-sm font-medium text-gray-700">
          Role Name
        </label>
        <input
          type="text"
          id="name"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          required
          className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
        />
      </div>

      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-700">
          Description
        </label>
        <input
          type="text"
          id="description"
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
        />
      </div>

      <div>
        <label htmlFor="permissions" className="block text-sm font-medium text-gray-700">
          Permissions (JSON)
        </label>
        <input
          type="text"
          id="permissions"
          value={formData.permissions}
          onChange={(e) => setFormData({ ...formData, permissions: e.target.value })}
          className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
        />
      </div>

      <div>
        <label htmlFor="priority" className="block text-sm font-medium text-gray-700">
          Priority
        </label>
        <input
          type="number"
          id="priority"
          value={formData.priority}
          onChange={(e) => setFormData({ ...formData, priority: parseInt(e.target.value) })}
          className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
        />
      </div>

      <div className="flex justify-end space-x-2">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 text-gray-600 hover:bg-gray-50 rounded"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          {isEdit ? "Update Role" : "Create Role"}
        </button>
      </div>
    </form>
  )
}

export default RoleManagement