/**
 * Integration tests for RBAC workflow
 * Tests the complete role-based access control workflow
 */

import { ReactNode } from "react"

import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { fireEvent, render, screen, waitFor } from "@testing-library/react"
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest"

import { auditApiClient } from "@/lib/api/audit"
import { authorizationApi } from "@/lib/api/endpoints/authorization"

import { RoleManagement } from "@/components/modules/auth/RoleManagement"

// Mock the API clients
vi.mock("@/lib/api/endpoints/authorization", () => ({
  authorizationApi: {
    getAllRoles: vi.fn(),
    createRole: vi.fn(),
    updateRole: vi.fn(),
    deleteRole: vi.fn(),
    getRoleHierarchy: vi.fn(),
  },
}))

vi.mock("@/lib/api/audit", () => ({
  auditApiClient: {
    logUserActivity: vi.fn(),
    logSecurityEvent: vi.fn(),
  },
}))

// Mock useToast
vi.mock("@/hooks/useToast", () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}))

// Mock data
const mockRoles = {
  items: [
    {
      id: 1,
      name: "Admin",
      description: "System administrator",
      is_system_role: true,
      is_active: true,
      permissions: '["read", "write", "delete", "admin"]',
      parent_role_id: undefined,
      priority: 100,
      notes: "System role",
      created_at: "2023-01-01T00:00:00Z",
      updated_at: "2023-01-01T00:00:00Z",
      is_deleted: false,
      deleted_at: undefined,
      deleted_by_user_id: undefined,
    },
    {
      id: 2,
      name: "Editor",
      description: "Content editor",
      is_system_role: false,
      is_active: true,
      permissions: '["read", "write"]',
      parent_role_id: undefined,
      priority: 50,
      notes: "Custom role",
      created_at: "2023-01-01T00:00:00Z",
      updated_at: "2023-01-01T00:00:00Z",
      is_deleted: false,
      deleted_at: undefined,
      deleted_by_user_id: undefined,
    },
    {
      id: 3,
      name: "Viewer",
      description: "Read-only access",
      is_system_role: false,
      is_active: true,
      permissions: '["read"]',
      parent_role_id: undefined,
      priority: 10,
      notes: "Basic role",
      created_at: "2023-01-01T00:00:00Z",
      updated_at: "2023-01-01T00:00:00Z",
      is_deleted: false,
      deleted_at: undefined,
      deleted_by_user_id: undefined,
    },
  ],
  pagination: {
    total: 3,
    page: 1,
    size: 10,
    pages: 1,
  },
}

const mockRoleHierarchy = [
  {
    id: 1,
    name: "Admin",
    description: "System administrator",
    is_system_role: true,
    is_active: true,
    permissions: '["read", "write", "delete", "admin"]',
    parent_role_id: undefined,
    priority: 100,
    notes: "System role",
    created_at: "2023-01-01T00:00:00Z",
    updated_at: "2023-01-01T00:00:00Z",
    is_deleted: false,
    deleted_at: undefined,
    deleted_by_user_id: undefined,
  },
  {
    id: 2,
    name: "Editor",
    description: "Content editor",
    is_system_role: false,
    is_active: true,
    permissions: '["read", "write"]',
    parent_role_id: 1,
    priority: 50,
    notes: "Custom role",
    created_at: "2023-01-01T00:00:00Z",
    updated_at: "2023-01-01T00:00:00Z",
    is_deleted: false,
    deleted_at: undefined,
    deleted_by_user_id: undefined,
  },
  {
    id: 3,
    name: "Viewer",
    description: "Read-only access",
    is_system_role: false,
    is_active: true,
    permissions: '["read"]',
    parent_role_id: 2,
    priority: 10,
    notes: "Basic role",
    created_at: "2023-01-01T00:00:00Z",
    updated_at: "2023-01-01T00:00:00Z",
    is_deleted: false,
    deleted_at: undefined,
    deleted_by_user_id: undefined,
  },
]

// Test wrapper with providers
const TestWrapper = ({ children }: { children: ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })

  return (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
}

describe("RBAC Workflow Integration", () => {
  beforeEach(() => {
    vi.clearAllMocks()

    // Setup default mock implementations
    vi.mocked(authorizationApi.getAllRoles).mockResolvedValue(mockRoles.items)
    vi.mocked(authorizationApi.getRoleHierarchy).mockResolvedValue(
      mockRoleHierarchy
    )
    vi.mocked(auditApiClient.logUserActivity).mockResolvedValue({} as any)
    vi.mocked(auditApiClient.logSecurityEvent).mockResolvedValue({} as any)
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe("Role Management Component", () => {
    it("should render role management interface", async () => {
      render(
        <TestWrapper>
          <RoleManagement />
        </TestWrapper>
      )

      // Check if the component renders
      expect(screen.getByText("Role Management")).toBeInTheDocument()
      expect(
        screen.getByText("Manage user roles and permissions")
      ).toBeInTheDocument()

      // Wait for roles to load
      await waitFor(() => {
        expect(screen.getByText("Admin")).toBeInTheDocument()
        expect(screen.getByText("Editor")).toBeInTheDocument()
        expect(screen.getByText("Viewer")).toBeInTheDocument()
      })
    })

    it("should display role hierarchy", async () => {
      render(
        <TestWrapper>
          <RoleManagement />
        </TestWrapper>
      )

      // Wait for roles to load
      await waitFor(() => {
        expect(screen.getByText("Admin")).toBeInTheDocument()
      })

      // Click on hierarchy tab
      const hierarchyTab = screen.getByText("Hierarchy")
      fireEvent.click(hierarchyTab)

      // Check if hierarchy is displayed
      await waitFor(() => {
        expect(screen.getByText("Role Hierarchy")).toBeInTheDocument()
      })
    })

    it("should handle role creation workflow", async () => {
      const mockCreateRole = vi.mocked(authorizationApi.createRole)
      mockCreateRole.mockResolvedValue({
        id: 4,
        name: "Test Role",
        description: "Test role description",
        is_system_role: false,
        is_active: true,
        permissions: '["read"]',
        parent_role_id: undefined,
        priority: 20,
        notes: "Test notes",
        created_at: "2023-01-01T00:00:00Z",
        updated_at: "2023-01-01T00:00:00Z",
        is_deleted: false,
        deleted_at: undefined,
        deleted_by_user_id: undefined,
      })

      render(
        <TestWrapper>
          <RoleManagement />
        </TestWrapper>
      )

      // Wait for component to load
      await waitFor(() => {
        expect(screen.getByText("Create Role")).toBeInTheDocument()
      })

      // Click create role button (get the first one which is the main button)
      const createButtons = screen.getAllByText("Create Role")
      const mainCreateButton = createButtons.find(button => 
        button.tagName === 'BUTTON' && !button.closest('[class*="fixed"]')
      ) || createButtons[0]
      fireEvent.click(mainCreateButton)

      // Fill in the form
      await waitFor(() => {
        expect(screen.getByLabelText("Role Name")).toBeInTheDocument()
      })

      const nameInput = screen.getByLabelText("Role Name")
      const descriptionInput = screen.getByLabelText("Description")
      const permissionsInput = screen.getByLabelText("Permissions (JSON)")
      const priorityInput = screen.getByLabelText("Priority")

      fireEvent.change(nameInput, { target: { value: "Test Role" } })
      fireEvent.change(descriptionInput, {
        target: { value: "Test role description" },
      })
      fireEvent.change(permissionsInput, { target: { value: '["read"]' } })
      fireEvent.change(priorityInput, { target: { value: "20" } })

      // Submit the form (get the submit button, which should be the one with type="submit")
      const submitButtons = screen.getAllByText("Create Role")
      const submitButton = submitButtons.find(button => 
        button.getAttribute('type') === 'submit'
      ) || submitButtons[submitButtons.length - 1]
      fireEvent.click(submitButton)

      // Verify API call
      await waitFor(() => {
        expect(mockCreateRole).toHaveBeenCalledWith({
          name: "Test Role",
          description: "Test role description",
          is_system_role: false,
          is_active: true,
          permissions: '["read"]',
          parent_role_id: undefined,
          priority: 20,
          notes: "",
        })
      })
    })

    it("should handle role update workflow", async () => {
      const mockUpdateRole = vi.mocked(authorizationApi.updateRole)
      mockUpdateRole.mockResolvedValue({
        ...mockRoles.items[1],
        name: "Updated Editor",
        description: "Updated description",
      })

      render(
        <TestWrapper>
          <RoleManagement />
        </TestWrapper>
      )

      // Wait for roles to load
      await waitFor(() => {
        expect(screen.getByText("Editor")).toBeInTheDocument()
      })

      // Find and click edit button for Editor role
      const editButtons = screen.getAllByText("Edit")
      fireEvent.click(editButtons[1]) // Editor is second in the list

      // Wait for edit dialog to open
      await waitFor(() => {
        expect(screen.getByText("Edit Role")).toBeInTheDocument()
      })

      // Update the role name
      const nameInput = screen.getByDisplayValue("Editor")
      fireEvent.change(nameInput, { target: { value: "Updated Editor" } })

      // Submit the form
      const updateButton = screen.getByText("Update Role")
      fireEvent.click(updateButton)

      // Verify API call
      await waitFor(() => {
        expect(mockUpdateRole).toHaveBeenCalledWith(2, {
          name: "Updated Editor",
          description: "Content editor",
          is_system_role: false,
          is_active: true,
          permissions: '["read", "write"]',
          parent_role_id: undefined,
          priority: 50,
          notes: "Custom role",
        })
      })
    })

    it("should handle role deletion workflow", async () => {
      const mockDeleteRole = vi.mocked(authorizationApi.deleteRole)
      mockDeleteRole.mockResolvedValue()

      // Mock window.confirm
      const confirmSpy = vi.spyOn(window, "confirm").mockReturnValue(true)

      render(
        <TestWrapper>
          <RoleManagement />
        </TestWrapper>
      )

      // Wait for roles to load
      await waitFor(() => {
        expect(screen.getByText("Editor")).toBeInTheDocument()
      })

      // Find and click delete button for Editor role
      const deleteButtons = screen.getAllByText("Delete")
      fireEvent.click(deleteButtons[1]) // Editor is second in the list

      // Verify confirmation dialog
      expect(confirmSpy).toHaveBeenCalledWith(
        "Are you sure you want to delete this role?"
      )

      // Verify API call
      await waitFor(() => {
        expect(mockDeleteRole).toHaveBeenCalledWith(2)
      })

      confirmSpy.mockRestore()
    })

    it("should prevent deletion of system roles", async () => {
      render(
        <TestWrapper>
          <RoleManagement />
        </TestWrapper>
      )

      // Wait for roles to load
      await waitFor(() => {
        expect(screen.getByText("Admin")).toBeInTheDocument()
      })

      // Find delete button for Admin role (system role)
      const deleteButtons = screen.getAllByText("Delete")
      const adminDeleteButton = deleteButtons[0] // Admin is first in the list

      // Verify the button is disabled
      expect(adminDeleteButton).toBeDisabled()
    })

    it("should handle API errors gracefully", async () => {
      const mockGetRoles = vi.mocked(authorizationApi.getAllRoles)
      mockGetRoles.mockRejectedValue(new Error("API Error"))

      render(
        <TestWrapper>
          <RoleManagement />
        </TestWrapper>
      )

      // Wait for error to appear
      await waitFor(() => {
        expect(screen.getByText("Error Loading Roles")).toBeInTheDocument()
        expect(screen.getByText("API Error")).toBeInTheDocument()
      })

      // Check if retry button is present
      expect(screen.getByText("Retry")).toBeInTheDocument()
    })

    it("should handle empty roles list", async () => {
      const mockGetRoles = vi.mocked(authorizationApi.getAllRoles)
      mockGetRoles.mockResolvedValue([])

      render(
        <TestWrapper>
          <RoleManagement />
        </TestWrapper>
      )

      // Wait for empty state
      await waitFor(() => {
        expect(screen.getByText("No roles found")).toBeInTheDocument()
      })
    })

    it("should handle pagination", async () => {
      const mockGetRoles = vi.mocked(authorizationApi.getAllRoles)
      mockGetRoles.mockResolvedValue(mockRoles.items)

      render(
        <TestWrapper>
          <RoleManagement />
        </TestWrapper>
      )

      // Wait for roles to load
      await waitFor(() => {
        expect(screen.getByText("Admin")).toBeInTheDocument()
      })

      // Check pagination controls (if they exist)
      const paginationText = screen.queryByText("Page 1 of 3")
      if (paginationText) {
        expect(paginationText).toBeInTheDocument()
      }
    })
  })

  describe("Role Hierarchy Display", () => {
    it("should display role hierarchy correctly", async () => {
      render(
        <TestWrapper>
          <RoleManagement />
        </TestWrapper>
      )

      // Wait for roles to load
      await waitFor(() => {
        expect(screen.getByText("Admin")).toBeInTheDocument()
      })

      // Click hierarchy tab
      const hierarchyTab = screen.getByText("Hierarchy")
      fireEvent.click(hierarchyTab)

      // Verify hierarchy structure
      await waitFor(() => {
        expect(screen.getByText("Role Hierarchy")).toBeInTheDocument()
        // Check if parent-child relationships are displayed
        expect(screen.getByText("Admin")).toBeInTheDocument()
        expect(screen.getByText("Editor")).toBeInTheDocument()
        expect(screen.getByText("Viewer")).toBeInTheDocument()
      })
    })

    it("should handle hierarchy loading errors", async () => {
      const mockGetRoleHierarchy = vi.mocked(authorizationApi.getRoleHierarchy)
      mockGetRoleHierarchy.mockRejectedValue(new Error("Hierarchy API Error"))

      render(
        <TestWrapper>
          <RoleManagement />
        </TestWrapper>
      )

      // Wait for roles to load
      await waitFor(() => {
        expect(screen.getByText("Admin")).toBeInTheDocument()
      })

      // Click hierarchy tab
      const hierarchyTab = screen.getByText("Hierarchy")
      fireEvent.click(hierarchyTab)

      // Should show loading initially then handle error gracefully
      await waitFor(() => {
        expect(
          screen.getByText("Loading role hierarchy...")
        ).toBeInTheDocument()
      })
    })
  })
})
