"use client"

/**
 * Custom hook for permission-based access control
 *
 * This hook provides a simple way to check if the current user
 * has permission to perform a specific action on a resource.
 */
import React, { useMemo } from "react"

import { useQueries } from "@tanstack/react-query"

import { QueryKeys } from "@/lib/api"
import { authorizationApi } from "@/lib/api/endpoints/authorization"
import { useHasPermission as useHasPermissionQuery } from "@/hooks/api/useAuthorization"

import { useAuthStore } from "../stores/authStore"
import type { UserRead } from "@/lib/api/types/auth"

export interface UseHasPermissionOptions {
  /**
   * The resource to check permission for (e.g., 'project', 'component')
   */
  resource: string

  /**
   * The action to check permission for (e.g., 'read', 'write', 'delete')
   */
  action: string

  /**
   * Whether to return true if the user is a superuser (default: true)
   */
  allowSuperuser?: boolean

  /**
   * Fallback value when permission check is loading or user is not authenticated
   */
  fallback?: boolean
}

export interface UseHasPermissionResult {
  /**
   * Whether the user has the requested permission
   */
  hasPermission: boolean

  /**
   * Whether the permission check is currently loading
   */
  isLoading: boolean

  /**
   * Whether there was an error checking the permission
   */
  isError: boolean

  /**
   * The error that occurred during permission check, if any
   */
  error: Error | null

  /**
   * Whether the user is authenticated
   */
  isAuthenticated: boolean

  /**
   * Whether the user is a superuser
   */
  isSuperuser: boolean
}

/**
 * Hook to check if the current user has a specific permission
 *
 * @param options - Permission check options
 * @returns Permission check result
 *
 * @example
 * ```tsx
 * function ComponentActions() {
 *   const { hasPermission, isLoading } = useHasPermission({
 *     resource: 'component',
 *     action: 'delete'
 *   })
 *
 *   if (isLoading) {
 *     return <LoadingSpinner />
 *   }
 *
 *   return (
 *     <>
 *       <Button>View Component</Button>
 *       {hasPermission && (
 *         <Button variant="destructive">Delete Component</Button>
 *       )}
 *     </>
 *   )
 * }
 * ```
 */
export function useHasPermission(
  options: UseHasPermissionOptions
): UseHasPermissionResult {
  const { resource, action, allowSuperuser = true, fallback = false } = options
  const { user: authUser, isAuthenticated } = useAuthStore()
  const user = authUser as UserRead | null

  // Use the authorization query hook for server-side permission check
  const permissionQuery = useHasPermissionQuery(resource, action) || {
    data: undefined,
    isLoading: false,
    isError: false,
    error: null,
  }

  // Memoize the result to avoid unnecessary re-renders
  const result = useMemo(() => {
    // If user is not authenticated, return fallback
    if (!isAuthenticated || !user) {
      return {
        hasPermission: fallback,
        isLoading: false,
        isError: false,
        error: null,
        isAuthenticated: false,
        isSuperuser: false,
      }
    }

    // If user is superuser and superuser access is allowed
    if (allowSuperuser && user.is_superuser) {
      return {
        hasPermission: true,
        isLoading: false,
        isError: false,
        error: null,
        isAuthenticated: true,
        isSuperuser: true,
      }
    }

    // Check if user has the permission in their cached permissions first
    const cachedPermission = `${resource}.${action}`
    if (user.permissions && user.permissions.includes(cachedPermission)) {
      return {
        hasPermission: true,
        isLoading: false,
        isError: false,
        error: null,
        isAuthenticated: true,
        isSuperuser: user.is_superuser,
      }
    }

    // Fall back to server-side permission check
    return {
      hasPermission: permissionQuery.data || false,
      isLoading: permissionQuery.isLoading,
      isError: permissionQuery.isError,
      error: permissionQuery.error,
      isAuthenticated: true,
      isSuperuser: user.is_superuser,
    }
  }, [
    isAuthenticated,
    user,
    resource,
    action,
    allowSuperuser,
    fallback,
    permissionQuery.data,
    permissionQuery.isLoading,
    permissionQuery.isError,
    permissionQuery.error,
  ])

  return result
}

/**
 * Higher-order component for conditional rendering based on permissions
 *
 * @param resource - The resource to check permission for
 * @param action - The action to check permission for
 * @param options - Additional options for permission checking
 *
 * @example
 * ```tsx
 * const DeleteButton = withPermission('component', 'delete')(() => (
 *   <Button variant="destructive">Delete Component</Button>
 * ))
 *
 * // Usage
 * <DeleteButton />
 * ```
 */
export function withPermission(
  resource: string,
  action: string,
  options?: Omit<UseHasPermissionOptions, "resource" | "action">
) {
  return function PermissionWrapper<P extends object>(
    Component: React.ComponentType<P>
  ): React.ComponentType<P> {
    return function WrappedComponent(props: P): React.ReactElement | null {
      const { hasPermission, isLoading } = useHasPermission({
        resource,
        action,
        ...options,
      })

      // Return null during loading or when permission is denied
      if (isLoading || !hasPermission) {
        return null
      }

      return React.createElement(Component, props)
    }
  }
}

/**
 * Utility function to check multiple permissions at once
 *
 * @param permissions - Array of resource.action strings to check
 * @param options - Additional options for permission checking
 *
 * @example
 * ```tsx
 * function AdminPanel() {
 *   const { hasAllPermissions, hasAnyPermission, results } = useHasMultiplePermissions([
 *     'user.create',
 *     'user.delete',
 *     'role.manage'
 *   ])
 *
 *   if (hasAllPermissions) {
 *     return <SuperAdminPanel />
 *   }
 *
 *   if (hasAnyPermission) {
 *     return <PartialAdminPanel permissions={results} />
 *   }
 *
 *   return <AccessDenied />
 * }
 * ```
 */
export function useHasMultiplePermissions(
  permissions: string[],
  options?: Omit<UseHasPermissionOptions, "resource" | "action">
) {
  // Preserve superuser fast-path and auth state
  const { user: authUser, isAuthenticated } = useAuthStore()
  const user = authUser as UserRead | null

  // Map permissions to resource/action pairs once
  const parsed = useMemo(
    () =>
      permissions.map((p) => {
        const [resource, action] = p.split(".")
        return { permission: p, resource, action }
      }),
    [permissions]
  )

  // For each permission, run a separate query using React Query useQueries
  const queries = useQueries({
    queries: parsed.map(({ resource, action }) => ({
      queryKey: QueryKeys.authorization.userPermission(
        user?.id!,
        resource,
        action
      ),
      queryFn: async (): Promise<boolean> =>
        await authorizationApi.checkUserPermission(user?.id!, resource, action),
      enabled: !!isAuthenticated && !!user && resource !== "" && action !== "",
      staleTime: 2 * 60 * 1000,
      gcTime: 5 * 60 * 1000,
    })),
  })

  const results = useMemo(() => {
    return parsed.map(({ permission, resource, action }, idx) => {
      // Compute hasPermission using same precedence as useHasPermission
      let hasPermission = false
      let isLoading = false
      let isError = false
      let error: any = null

      // Not authenticated -> fallback false
      if (!isAuthenticated || !user) {
        hasPermission = options?.fallback ?? false
        isLoading = false
        isError = false
        error = null
      } else if ((options?.allowSuperuser ?? true) && user.is_superuser) {
        hasPermission = true
        isLoading = false
        isError = false
        error = null
      } else if (
        user.permissions &&
        user.permissions.includes(`${resource}.${action}`)
      ) {
        hasPermission = true
        isLoading = false
        isError = false
        error = null
      } else {
        // Use server-side query for this permission
        const q = queries[idx]
        if (q) {
          hasPermission = (q.data as boolean) ?? false
          isLoading = q.isLoading
          isError = q.isError
          error = q.error
        }
      }

      return {
        permission,
        resource,
        action,
        hasPermission,
        isLoading,
        isError,
        error,
        isAuthenticated: !!isAuthenticated,
        isSuperuser: !!user?.is_superuser,
      }
    })
  }, [
    parsed,
    queries,
    isAuthenticated,
    user,
    options?.fallback,
    options?.allowSuperuser,
  ])

  const hasAllPermissions = results.every((r) => r.hasPermission)
  const hasAnyPermission = results.some((r) => r.hasPermission)
  const anyLoading = results.some((r) => r.isLoading)
  const anyError = results.some((r) => r.isError)

  return {
    hasAllPermissions,
    hasAnyPermission,
    isLoading: anyLoading,
    isError: anyError,
    results,
  }
}
