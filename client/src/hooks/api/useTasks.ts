/**
 * Task Management React Query Hooks
 */

import type {
  TaskAdvancedSearch,
  TaskBulkAssignment,
  TaskBulkCreate,
  TaskBulkStatusUpdate,
  TaskBulkUpdate,
  TaskCommentCreate,
  TaskCreate,
  TaskDependencyCreate,
  TaskExportParams,
  TaskImportData,
  TaskListParams,
  TaskRead,
  TaskSearchParams,
  TaskStatus,
  TaskTimeEntryCreate,
  TaskUpdate,
} from "../../lib/api/types/tasks"

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"

import { tasksApi } from "../../lib/api/endpoints"
import { QueryKeys } from "../../lib/api/keys"

export function useTasks(params?: TaskListParams) {
  return useQuery({
    queryKey: QueryKeys.tasks.list(params as Record<string, unknown>),
    queryFn: () => tasksApi.list(params),
    staleTime: 2 * 60 * 1000,
  })
}

export function useTask(id: number) {
  return useQuery({
    queryKey: QueryKeys.tasks.detail(id),
    queryFn: () => tasksApi.get(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  })
}

export function useTaskByTaskId(taskId: string) {
  return useQuery({
    queryKey: QueryKeys.tasks.byTaskId(taskId),
    queryFn: () => tasksApi.getByTaskId(taskId),
    enabled: !!taskId,
    staleTime: 5 * 60 * 1000,
  })
}

export function useCreateTask() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["tasks", "create"] as const,
    mutationFn: (data: TaskCreate) => tasksApi.create(data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.tasks.all })
      if (data.project_id) {
        queryClient.invalidateQueries({
          queryKey: QueryKeys.tasks.byProject(data.project_id),
        })
      }
    },
  })
}

export function useUpdateTask() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["tasks", "update"] as const,
    mutationFn: ({ id, data }: { id: number; data: TaskUpdate }) =>
      tasksApi.update(id, data),
    onSuccess: (data, { id }) => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.tasks.detail(id) })
      queryClient.invalidateQueries({ queryKey: QueryKeys.tasks.all })
      if (data.project_id) {
        queryClient.invalidateQueries({
          queryKey: QueryKeys.tasks.byProject(data.project_id),
        })
      }
    },
  })
}

export function useDeleteTask() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["tasks", "delete"] as const,
    mutationFn: (id: number) => tasksApi.delete(id),
    onSuccess: (_, id) => {
      queryClient.removeQueries({ queryKey: QueryKeys.tasks.detail(id) })
      queryClient.invalidateQueries({ queryKey: QueryKeys.tasks.all })
    },
  })
}

export function useSearchTasks() {
  return useMutation({
    mutationFn: (params: TaskSearchParams) => tasksApi.search(params),
  })
}

export function useAdvancedSearchTasks() {
  return useMutation({
    mutationFn: (criteria: TaskAdvancedSearch) =>
      tasksApi.advancedSearch(criteria),
  })
}

export function useTasksByProject(projectId: number, params?: TaskListParams) {
  return useQuery({
    queryKey: QueryKeys.tasks.byProject(
      projectId,
      params as Record<string, unknown>
    ),
    queryFn: () => tasksApi.getByProject(projectId, params),
    enabled: !!projectId,
    staleTime: 2 * 60 * 1000,
  })
}

export function useOverdueTasks(params?: TaskListParams) {
  return useQuery({
    queryKey: QueryKeys.tasks.overdue(params as Record<string, unknown>),
    queryFn: () => tasksApi.getOverdue(params),
    staleTime: 1 * 60 * 1000,
  })
}

export function useUpdateTaskStatus() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["tasks", "updateStatus"] as const,
    mutationFn: ({
      id,
      status,
      reason,
    }: {
      id: number
      status: TaskStatus
      reason?: string
    }) => tasksApi.updateStatus(id, status, reason),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.tasks.detail(id) })
      queryClient.invalidateQueries({ queryKey: QueryKeys.tasks.all })
    },
  })
}

export function useMarkTaskComplete() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["tasks", "markComplete"] as const,
    mutationFn: ({
      id,
      completionNotes,
    }: {
      id: number
      completionNotes?: string
    }) => tasksApi.markComplete(id, completionNotes),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.tasks.detail(id) })
      queryClient.invalidateQueries({ queryKey: QueryKeys.tasks.all })
    },
  })
}

export function useTaskAssignments(id: number) {
  return useQuery({
    queryKey: QueryKeys.tasks.assignments(id),
    queryFn: () => tasksApi.getAssignments(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  })
}

export function useAssignTask() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["tasks", "assign"] as const,
    mutationFn: ({ id, userIds }: { id: number; userIds: number[] }) =>
      tasksApi.assign(id, userIds),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.tasks.assignments(id),
      })
      queryClient.invalidateQueries({ queryKey: QueryKeys.tasks.detail(id) })
    },
  })
}

export function useUnassignTask() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["tasks", "unassign"] as const,
    mutationFn: ({ id, userIds }: { id: number; userIds: number[] }) =>
      tasksApi.unassign(id, userIds),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.tasks.assignments(id),
      })
      queryClient.invalidateQueries({ queryKey: QueryKeys.tasks.detail(id) })
    },
  })
}

export function useTaskDependencies(id: number) {
  return useQuery({
    queryKey: QueryKeys.tasks.dependencies(id),
    queryFn: () => tasksApi.getDependencies(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000,
  })
}

export function useAddTaskDependency() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["tasks", "addDependency"] as const,
    mutationFn: ({ id, data }: { id: number; data: TaskDependencyCreate }) =>
      tasksApi.addDependency(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.tasks.dependencies(id),
      })
      queryClient.invalidateQueries({ queryKey: QueryKeys.tasks.detail(id) })
    },
  })
}

export function useRemoveTaskDependency() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["tasks", "removeDependency"] as const,
    mutationFn: ({ id, dependencyId }: { id: number; dependencyId: number }) =>
      tasksApi.removeDependency(id, dependencyId),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.tasks.dependencies(id),
      })
      queryClient.invalidateQueries({ queryKey: QueryKeys.tasks.detail(id) })
    },
  })
}

export function useTaskComments(
  id: number,
  params?: { page?: number; size?: number }
) {
  return useQuery({
    queryKey: QueryKeys.tasks.comments(id, params),
    queryFn: () => tasksApi.getComments(id, params),
    enabled: !!id,
    staleTime: 2 * 60 * 1000,
  })
}

export function useAddTaskComment() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["tasks", "addComment"] as const,
    mutationFn: ({ id, data }: { id: number; data: TaskCommentCreate }) =>
      tasksApi.addComment(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.tasks.comments(id) })
    },
  })
}

export function useTaskTimeEntries(
  id: number,
  params?: { page?: number; size?: number }
) {
  return useQuery({
    queryKey: QueryKeys.tasks.timeEntries(id, params),
    queryFn: () => tasksApi.getTimeEntries(id, params),
    enabled: !!id,
    staleTime: 2 * 60 * 1000,
  })
}

export function useStartTaskTimer() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["tasks", "startTimer"] as const,
    mutationFn: ({ id, description }: { id: number; description?: string }) =>
      tasksApi.startTimer(id, description),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.tasks.timeEntries(id),
      })
    },
  })
}

export function useStopTaskTimer() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["tasks", "stopTimer"] as const,
    mutationFn: ({ id, entryId }: { id: number; entryId: number }) =>
      tasksApi.stopTimer(id, entryId),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.tasks.timeEntries(id),
      })
    },
  })
}

export function useAddTaskTimeEntry() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["tasks", "addTimeEntry"] as const,
    mutationFn: ({ id, data }: { id: number; data: TaskTimeEntryCreate }) =>
      tasksApi.addTimeEntry(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.tasks.timeEntries(id),
      })
    },
  })
}

export function useTaskStatistics() {
  return useQuery({
    queryKey: QueryKeys.tasks.statistics,
    queryFn: () => tasksApi.getStatistics(),
    staleTime: 10 * 60 * 1000,
  })
}

export function useProjectTaskStatistics(projectId: number) {
  return useQuery({
    queryKey: QueryKeys.tasks.projectStatistics(projectId),
    queryFn: () => tasksApi.getProjectStatistics(projectId),
    enabled: !!projectId,
    staleTime: 5 * 60 * 1000,
  })
}

export function useBulkCreateTasks() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["tasks", "bulkCreate"] as const,
    mutationFn: (data: TaskBulkCreate) => tasksApi.bulkCreate(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.tasks.all })
    },
  })
}

export function useBulkUpdateTasks() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["tasks", "bulkUpdate"] as const,
    mutationFn: (data: TaskBulkUpdate) => tasksApi.bulkUpdate(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.tasks.all })
    },
  })
}

export function useBulkStatusUpdateTasks() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["tasks", "bulkStatusUpdate"] as const,
    mutationFn: (data: TaskBulkStatusUpdate) => tasksApi.bulkStatusUpdate(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.tasks.all })
    },
  })
}

export function useBulkAssignTasks() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["tasks", "bulkAssign"] as const,
    mutationFn: (data: TaskBulkAssignment) => tasksApi.bulkAssign(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.tasks.all })
    },
  })
}

export function useImportTasks() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["tasks", "import"] as const,
    mutationFn: (data: TaskImportData[]) => tasksApi.import(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.tasks.all })
    },
  })
}

export function useExportTasks() {
  return useMutation({
    mutationFn: (params: TaskExportParams) => tasksApi.export(params),
  })
}
