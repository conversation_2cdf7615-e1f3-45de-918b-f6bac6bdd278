/**
 * Project Management React Query Hooks
 */

import type {
  ProjectAdvancedSearch,
  ProjectBulkStatusUpdate,
  ProjectBulkUpdate,
  ProjectCreate,
  ProjectExportParams,
  ProjectImportData,
  ProjectListParams,
  ProjectMemberCreate,
  ProjectMemberUpdate,
  ProjectRead,
  ProjectSearchParams,
  ProjectStatus,
  ProjectUpdate,
} from "../../lib/api/types/projects"

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"

import { projectsApi } from "../../lib/api/endpoints"
import { QueryKeys } from "../../lib/api/keys"

export function useProjects(params?: ProjectListParams) {
  return useQuery({
    queryKey: QueryKeys.projects.list(params as Record<string, unknown>),
    queryFn: () => projectsApi.list(params),
    staleTime: 5 * 60 * 1000,
  })
}

export function useProject(id: number) {
  return useQuery({
    queryKey: QueryKeys.projects.detail(id),
    queryFn: () => projectsApi.get(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000,
  })
}

export function useCreateProject() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["projects", "create"] as const,
    mutationFn: (data: ProjectCreate) => projectsApi.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.projects.all })
    },
  })
}

export function useUpdateProject() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["projects", "update"] as const,
    mutationFn: ({ id, data }: { id: number; data: ProjectUpdate }) =>
      projectsApi.update(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.projects.detail(id) })
      queryClient.invalidateQueries({ queryKey: QueryKeys.projects.all })
    },
  })
}

export function useDeleteProject() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["projects", "delete"] as const,
    mutationFn: (id: number) => projectsApi.delete(id),
    onSuccess: (_, id) => {
      queryClient.removeQueries({ queryKey: QueryKeys.projects.detail(id) })
      queryClient.invalidateQueries({ queryKey: QueryKeys.projects.all })
    },
  })
}

export function useSearchProjects() {
  return useMutation({
    mutationFn: (params: ProjectSearchParams) => projectsApi.search(params),
  })
}

export function useAdvancedSearchProjects() {
  return useMutation({
    mutationFn: (criteria: ProjectAdvancedSearch) =>
      projectsApi.advancedSearch(criteria),
  })
}

export function useProjectStats() {
  return useQuery({
    queryKey: QueryKeys.projects.stats,
    queryFn: () => projectsApi.getStats(),
    staleTime: 10 * 60 * 1000,
  })
}

export function useUpdateProjectStatus() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["projects", "updateStatus"] as const,
    mutationFn: ({
      id,
      status,
      reason,
    }: {
      id: number
      status: ProjectStatus
      reason?: string
    }) => projectsApi.updateStatus(id, status, reason),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.projects.detail(id) })
      queryClient.invalidateQueries({ queryKey: QueryKeys.projects.all })
    },
  })
}

export function useProjectMembers(
  id: number,
  params?: { is_active?: boolean; role_id?: number }
) {
  return useQuery({
    queryKey: QueryKeys.projects.members(id, params),
    queryFn: () => projectsApi.getMembers(id, params),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  })
}

export function useAddProjectMember() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["projects", "addMember"] as const,
    mutationFn: ({ id, data }: { id: number; data: ProjectMemberCreate }) =>
      projectsApi.addMember(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.projects.members(id),
      })
      queryClient.invalidateQueries({ queryKey: QueryKeys.projects.detail(id) })
    },
  })
}

export function useUpdateProjectMember() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["projects", "updateMember"] as const,
    mutationFn: ({
      id,
      memberId,
      data,
    }: {
      id: number
      memberId: number
      data: ProjectMemberUpdate
    }) => projectsApi.updateMember(id, memberId, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.projects.members(id),
      })
    },
  })
}

export function useRemoveProjectMember() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["projects", "removeMember"] as const,
    mutationFn: ({ id, memberId }: { id: number; memberId: number }) =>
      projectsApi.removeMember(id, memberId),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.projects.members(id),
      })
      queryClient.invalidateQueries({ queryKey: QueryKeys.projects.detail(id) })
    },
  })
}

export function useBulkUpdateProjects() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["projects", "bulkUpdate"] as const,
    mutationFn: (data: ProjectBulkUpdate) => projectsApi.bulkUpdate(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.projects.all })
    },
  })
}

export function useBulkStatusUpdateProjects() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["projects", "bulkStatusUpdate"] as const,
    mutationFn: (data: ProjectBulkStatusUpdate) =>
      projectsApi.bulkStatusUpdate(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.projects.all })
    },
  })
}

export function useArchiveProject() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["projects", "archive"] as const,
    mutationFn: (id: number) => projectsApi.archive(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.projects.detail(id) })
      queryClient.invalidateQueries({ queryKey: QueryKeys.projects.all })
    },
  })
}

export function useUnarchiveProject() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["projects", "unarchive"] as const,
    mutationFn: (id: number) => projectsApi.unarchive(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.projects.detail(id) })
      queryClient.invalidateQueries({ queryKey: QueryKeys.projects.all })
    },
  })
}

export function useProjectActivity(
  id: number,
  params?: { page?: number; size?: number }
) {
  return useQuery({
    queryKey: QueryKeys.projects.activity(id, params),
    queryFn: () => projectsApi.getActivity(id, params),
    enabled: !!id,
    staleTime: 2 * 60 * 1000,
  })
}

export function useProjectNotifications(id: number) {
  return useQuery({
    queryKey: QueryKeys.projects.notifications(id),
    queryFn: () => projectsApi.getNotifications(id),
    enabled: !!id,
    staleTime: 1 * 60 * 1000,
  })
}

export function useMarkProjectNotificationRead() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["projects", "markNotificationRead"] as const,
    mutationFn: ({
      id,
      notificationId,
    }: {
      id: number
      notificationId: number
    }) => projectsApi.markNotificationRead(id, notificationId),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.projects.notifications(id),
      })
    },
  })
}

export function useProjectTemplates() {
  return useQuery({
    queryKey: QueryKeys.projects.templates,
    queryFn: () => projectsApi.getTemplates(),
    staleTime: 30 * 60 * 1000,
  })
}

export function useCreateProjectFromTemplate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["projects", "createFromTemplate"] as const,
    mutationFn: ({
      templateId,
      data,
    }: {
      templateId: number
      data: {
        name: string
        description?: string
        client?: string
        location?: string
      }
    }) => projectsApi.createFromTemplate(templateId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.projects.all })
    },
  })
}

export function useInviteToProject() {
  return useMutation({
    mutationKey: ["projects", "invite"] as const,
    mutationFn: ({
      id,
      data,
    }: {
      id: number
      data: {
        email: string
        role_id: number
        message?: string
      }
    }) => projectsApi.invite(id, data),
  })
}

export function useAcceptProjectInvitation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["projects", "acceptInvitation"] as const,
    mutationFn: (token: string) => projectsApi.acceptInvitation(token),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.projects.all })
    },
  })
}

export function useDuplicateProject() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["projects", "duplicate"] as const,
    mutationFn: ({
      id,
      data,
    }: {
      id: number
      data: {
        name: string
        include_members?: boolean
        include_tasks?: boolean
      }
    }) => projectsApi.duplicate(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.projects.all })
    },
  })
}

export function useImportProjects() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["projects", "import"] as const,
    mutationFn: (data: ProjectImportData[]) => projectsApi.import(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.projects.all })
    },
  })
}

export function useExportProjects() {
  return useMutation({
    mutationFn: (params: ProjectExportParams) => projectsApi.export(params),
  })
}
