/**
 * Comprehensive test suite for Authorization hooks
 *
 * Tests React Query hooks for RBAC functionality including
 * role management, permission checking, and user assignments.
 */

import { ReactNode } from "react"

import type {
  Permission,
  PermissionCreate,
  Role,
  RoleCreate,
  RoleUpdate,
  UserRoleAssignmentCreate,
} from "@/lib/api/types/authorization"

import * as authStore from "@/stores/authStore"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { renderHook, waitFor } from "@testing-library/react"
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest"

import * as authorizationApi from "@/lib/api/endpoints/authorization"

import {
  useAssignPermissionsToRole,
  useAssignRoleToUser,
  useCreatePermission,
  useCreateRole,
  useCurrentUserPermissions,
  useHasPermission,
  usePermissions,
  useRemoveRoleFromUser,
  useRole,
  useRoles,
  useUpdateRole,
  useUserPermission,
  useUserPermissions,
} from "../useAuthorization"

// Mock the API module
vi.mock("@/lib/api/endpoints/authorization", () => {
  const getAllRoles = vi.fn()
  const getRoleById = vi.fn()
  const createRole = vi.fn()
  const updateRole = vi.fn()
  const getAllPermissions = vi.fn()
  const createPermission = vi.fn()
  const assignPermissionsToRole = vi.fn()
  const assignRoleToUser = vi.fn()
  const removeRoleFromUser = vi.fn()
  const checkUserPermission = vi.fn()
  const getUserPermissions = vi.fn()

  return {
    getAllRoles,
    getRoleById,
    createRole,
    updateRole,
    getAllPermissions,
    createPermission,
    assignPermissionsToRole,
    assignRoleToUser,
    removeRoleFromUser,
    checkUserPermission,
    getUserPermissions,
    authorizationApi: {
      getAllRoles,
      getRoleById,
      createRole,
      updateRole,
      getAllPermissions,
      createPermission,
      assignPermissionsToRole,
      assignRoleToUser,
      removeRoleFromUser,
      checkUserPermission,
      getUserPermissions,
    },
  }
})

// Mock the auth store
vi.mock("@/stores/authStore", () => ({
  useAuthStore: vi.fn(),
}))

// Test data
const mockRoles: Role[] = [
  {
    id: 1,
    name: "Admin",
    description: "Administrator role",
    is_system_role: false,
    is_active: true,
    priority: 100,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
  },
  {
    id: 2,
    name: "User",
    description: "Regular user role",
    is_system_role: false,
    is_active: true,
    priority: 200,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
  },
]

const mockPermissions: Permission[] = [
  {
    id: 1,
    name: "Read Projects",
    resource: "project",
    action: "read",
    description: "Permission to read projects",
    is_system_permission: false,
    is_active: true,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
  },
  {
    id: 2,
    name: "Write Projects",
    resource: "project",
    action: "write",
    description: "Permission to write projects",
    is_system_permission: false,
    is_active: true,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
  },
]

const mockUser = {
  id: 1,
  name: "Test User",
  email: "<EMAIL>",
  is_superuser: false,
  is_active: true,
  permissions: ["project.read"],
  created_at: "2024-01-01T00:00:00Z",
  updated_at: "2024-01-01T00:00:00Z",
}

// Test wrapper with QueryClient
function createWrapper() {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  const Wrapper = ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
  Wrapper.displayName = "QueryClientTestWrapper"
  return Wrapper
}

describe("Authorization Hooks", () => {
  let queryClient: QueryClient

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    })
    vi.clearAllMocks()
  })

  afterEach(() => {
    queryClient.clear()
  })

  describe("useRoles", () => {
    it("should fetch all roles when authenticated", async () => {
      // Arrange
      vi.mocked(authStore.useAuthStore).mockReturnValue({
        isAuthenticated: true,
        user: mockUser,
        token: "mock-token",
        isLoading: false,
        lastActivity: null,
        setAuth: vi.fn(),
        updateUser: vi.fn(),
        clearAuth: vi.fn(),
        setLoading: vi.fn(),
        initializeAuth: vi.fn(),
      })
      vi.mocked(authorizationApi.getAllRoles).mockResolvedValue(mockRoles)

      const wrapper = createWrapper()

      // Act
      const { result } = renderHook(() => useRoles(), { wrapper })

      // Assert
      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockRoles)
      expect(authorizationApi.getAllRoles).toHaveBeenCalledTimes(1)
    })

    it("should not fetch roles when not authenticated", () => {
      // Arrange
      vi.mocked(authStore.useAuthStore).mockReturnValue({
        isAuthenticated: false,
        user: null,
        token: null,
        isLoading: false,
        lastActivity: null,
        setAuth: vi.fn(),
        updateUser: vi.fn(),
        clearAuth: vi.fn(),
        setLoading: vi.fn(),
        initializeAuth: vi.fn(),
      })

      const wrapper = createWrapper()

      // Act
      const { result } = renderHook(() => useRoles(), { wrapper })

      // Assert
      expect(result.current.isLoading).toBe(false)
      expect(result.current.data).toBeUndefined()
      expect(authorizationApi.getAllRoles).not.toHaveBeenCalled()
    })

    it("should handle API errors gracefully", async () => {
      // Arrange
      vi.mocked(authStore.useAuthStore).mockReturnValue({
        isAuthenticated: true,
        user: mockUser,
        token: "mock-token",
        isLoading: false,
        lastActivity: null,
        setAuth: vi.fn(),
        updateUser: vi.fn(),
        clearAuth: vi.fn(),
        setLoading: vi.fn(),
        initializeAuth: vi.fn(),
      })
      vi.mocked(authorizationApi.getAllRoles).mockRejectedValue(
        new Error("API Error")
      )

      const wrapper = createWrapper()

      // Act
      const { result } = renderHook(() => useRoles(), { wrapper })

      // Assert
      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error).toEqual(new Error("API Error"))
    })
  })

  describe("useRole", () => {
    it("should fetch specific role by ID when authenticated", async () => {
      // Arrange
      const roleId = 1
      const mockRole = mockRoles[0]

      vi.mocked(authStore.useAuthStore).mockReturnValue({
        isAuthenticated: true,
        user: mockUser,
        token: "mock-token",
        isLoading: false,
        lastActivity: null,
        setAuth: vi.fn(),
        updateUser: vi.fn(),
        clearAuth: vi.fn(),
        setLoading: vi.fn(),
        initializeAuth: vi.fn(),
      })
      vi.mocked(authorizationApi.getRoleById).mockResolvedValue(mockRole)

      const wrapper = createWrapper()

      // Act
      const { result } = renderHook(() => useRole(roleId), { wrapper })

      // Assert
      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockRole)
      expect(authorizationApi.getRoleById).toHaveBeenCalledWith(roleId)
    })

    it("should not fetch when roleId is undefined", () => {
      // Arrange
      vi.mocked(authStore.useAuthStore).mockReturnValue({
        isAuthenticated: true,
        user: mockUser,
        token: "mock-token",
        isLoading: false,
        lastActivity: null,
        setAuth: vi.fn(),
        updateUser: vi.fn(),
        clearAuth: vi.fn(),
        setLoading: vi.fn(),
        initializeAuth: vi.fn(),
      })

      const wrapper = createWrapper()

      // Act
      const { result } = renderHook(() => useRole(undefined), { wrapper })

      // Assert
      expect(result.current.isLoading).toBe(false)
      expect(authorizationApi.getRoleById).not.toHaveBeenCalled()
    })
  })

  describe("useCreateRole", () => {
    it("should create a new role successfully", async () => {
      // Arrange
      const newRoleData: RoleCreate = {
        name: "New Role",
        description: "New test role",
        priority: 300,
      }
      const createdRole: Role = {
        id: 3,
        name: newRoleData.name,
        description: newRoleData.description,
        is_system_role: false,
        is_active: true,
        priority: newRoleData.priority || 0,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
      }

      vi.mocked(authorizationApi.createRole).mockResolvedValue(createdRole)

      const wrapper = createWrapper()

      // Act
      const { result } = renderHook(() => useCreateRole(), { wrapper })

      // Assert
      expect(result.current.mutateAsync).toBeDefined()

      // Trigger mutation
      const mutationResult = await result.current.mutateAsync(newRoleData)
      expect(mutationResult).toEqual(createdRole)
      expect(authorizationApi.createRole).toHaveBeenCalledWith(newRoleData)
    })

    it("should handle creation errors", async () => {
      // Arrange
      const newRoleData: RoleCreate = {
        name: "Invalid Role",
        description: "This will fail",
      }

      vi.mocked(authorizationApi.createRole).mockRejectedValue(
        new Error("Validation Error")
      )

      const wrapper = createWrapper()

      // Act
      const { result } = renderHook(() => useCreateRole(), { wrapper })

      // Assert
      await expect(result.current.mutateAsync(newRoleData)).rejects.toThrow(
        "Validation Error"
      )
    })
  })

  describe("useUpdateRole", () => {
    it("should update a role successfully", async () => {
      // Arrange
      const roleId = 1
      const updateData: RoleUpdate = {
        name: "Updated Role",
        description: "Updated description",
      }
      const updatedRole: Role = {
        ...mockRoles[0],
        ...updateData,
      }

      vi.mocked(authorizationApi.updateRole).mockResolvedValue(updatedRole)

      const wrapper = createWrapper()

      // Act
      const { result } = renderHook(() => useUpdateRole(), { wrapper })

      // Trigger mutation
      const mutationResult = await result.current.mutateAsync({
        roleId,
        roleData: updateData,
      })

      // Assert
      expect(mutationResult).toEqual(updatedRole)
      expect(authorizationApi.updateRole).toHaveBeenCalledWith(
        roleId,
        updateData
      )
    })
  })

  describe("usePermissions", () => {
    it("should fetch all permissions when authenticated", async () => {
      // Arrange
      vi.mocked(authStore.useAuthStore).mockReturnValue({
        isAuthenticated: true,
        user: mockUser,
        token: "mock-token",
        isLoading: false,
        lastActivity: null,
        setAuth: vi.fn(),
        updateUser: vi.fn(),
        clearAuth: vi.fn(),
        setLoading: vi.fn(),
        initializeAuth: vi.fn(),
      })
      vi.mocked(authorizationApi.getAllPermissions).mockResolvedValue(
        mockPermissions
      )

      const wrapper = createWrapper()

      // Act
      const { result } = renderHook(() => usePermissions(), { wrapper })

      // Assert
      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockPermissions)
      expect(authorizationApi.getAllPermissions).toHaveBeenCalledTimes(1)
    })
  })

  describe("useCreatePermission", () => {
    it("should create a new permission successfully", async () => {
      // Arrange
      const newPermissionData: PermissionCreate = {
        name: "Delete Projects",
        resource: "project",
        action: "delete",
        description: "Permission to delete projects",
      }
      const createdPermission: Permission = {
        id: 3,
        ...newPermissionData,
        is_system_permission: false,
        is_active: true,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
      }

      vi.mocked(authorizationApi.createPermission).mockResolvedValue(
        createdPermission
      )

      const wrapper = createWrapper()

      // Act
      const { result } = renderHook(() => useCreatePermission(), { wrapper })

      // Trigger mutation
      const mutationResult = await result.current.mutateAsync(newPermissionData)

      // Assert
      expect(mutationResult).toEqual(createdPermission)
      expect(authorizationApi.createPermission).toHaveBeenCalledWith(
        newPermissionData
      )
    })
  })

  describe("useAssignPermissionsToRole", () => {
    it("should assign permissions to role successfully", async () => {
      // Arrange
      const roleId = 1
      const permissionIds = [1, 2]
      const updatedRole = {
        ...mockRoles[0],
        permissions: "project.read,project.write",
      }

      vi.mocked(authorizationApi.assignPermissionsToRole).mockResolvedValue(
        updatedRole
      )

      const wrapper = createWrapper()

      // Act
      const { result } = renderHook(() => useAssignPermissionsToRole(), {
        wrapper,
      })

      // Trigger mutation
      const mutationResult = await result.current.mutateAsync({
        roleId,
        permissionIds,
      })

      // Assert
      expect(mutationResult).toEqual(updatedRole)
      expect(authorizationApi.assignPermissionsToRole).toHaveBeenCalledWith(
        roleId,
        permissionIds
      )
    })
  })

  describe("useAssignRoleToUser", () => {
    it("should assign role to user successfully", async () => {
      // Arrange
      const userId = 1
      const assignmentData: UserRoleAssignmentCreate = {
        role_id: 1,
        expires_at: "2024-12-31T00:00:00Z",
      }
      const assignment = {
        id: 1,
        name: "Test Assignment",
        user_id: userId,
        role_id: 1,
        assigned_by_user_id: 1,
        assigned_at: "2024-01-01T00:00:00Z",
        expires_at: "2024-12-31T00:00:00Z",
        is_active: true,
        assignment_context: undefined,
        notes: undefined,
        is_expired: false,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
      }

      vi.mocked(authorizationApi.assignRoleToUser).mockResolvedValue(assignment)

      const wrapper = createWrapper()

      // Act
      const { result } = renderHook(() => useAssignRoleToUser(), { wrapper })

      // Trigger mutation
      const mutationResult = await result.current.mutateAsync({
        userId,
        assignmentData,
      })

      // Assert
      expect(mutationResult).toEqual(assignment)
      expect(authorizationApi.assignRoleToUser).toHaveBeenCalledWith(
        userId,
        assignmentData
      )
    })
  })

  describe("useRemoveRoleFromUser", () => {
    it("should remove role from user successfully", async () => {
      // Arrange
      const userId = 1
      const roleId = 1

      vi.mocked(authorizationApi.removeRoleFromUser).mockResolvedValue(
        undefined
      )

      const wrapper = createWrapper()

      // Act
      const { result } = renderHook(() => useRemoveRoleFromUser(), { wrapper })

      // Trigger mutation
      await result.current.mutateAsync({ userId, roleId })

      // Assert
      expect(authorizationApi.removeRoleFromUser).toHaveBeenCalledWith(
        userId,
        roleId
      )
    })
  })

  describe("useUserPermission", () => {
    it("should check user permission when authenticated", async () => {
      // Arrange
      const userId = 1
      const resource = "project"
      const action = "read"

      vi.mocked(authStore.useAuthStore).mockReturnValue({
        isAuthenticated: true,
        user: mockUser,
        token: "mock-token",
        isLoading: false,
        lastActivity: null,
        setAuth: vi.fn(),
        updateUser: vi.fn(),
        clearAuth: vi.fn(),
        setLoading: vi.fn(),
        initializeAuth: vi.fn(),
      })
      vi.mocked(authorizationApi.checkUserPermission).mockResolvedValue(true)

      const wrapper = createWrapper()

      // Act
      const { result } = renderHook(
        () => useUserPermission(userId, resource, action),
        { wrapper }
      )

      // Assert
      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toBe(true)
      expect(authorizationApi.checkUserPermission).toHaveBeenCalledWith(
        userId,
        resource,
        action
      )
    })

    it("should not fetch when parameters are invalid", () => {
      // Arrange
      vi.mocked(authStore.useAuthStore).mockReturnValue({
        isAuthenticated: true,
        user: mockUser,
        token: "mock-token",
        isLoading: false,
        lastActivity: null,
        setAuth: vi.fn(),
        updateUser: vi.fn(),
        clearAuth: vi.fn(),
        setLoading: vi.fn(),
        initializeAuth: vi.fn(),
      })

      const wrapper = createWrapper()

      // Act
      const { result } = renderHook(
        () => useUserPermission(undefined, "", "read"),
        { wrapper }
      )

      // Assert
      expect(result.current.isLoading).toBe(false)
      expect(authorizationApi.checkUserPermission).not.toHaveBeenCalled()
    })
  })

  describe("useUserPermissions", () => {
    it("should fetch user permissions when authenticated", async () => {
      // Arrange
      const userId = 1
      const userPermissions = ["project.read", "project.write"]

      vi.mocked(authStore.useAuthStore).mockReturnValue({
        isAuthenticated: true,
        user: mockUser,
        token: "mock-token",
        isLoading: false,
        lastActivity: null,
        setAuth: vi.fn(),
        updateUser: vi.fn(),
        clearAuth: vi.fn(),
        setLoading: vi.fn(),
        initializeAuth: vi.fn(),
      })
      vi.mocked(authorizationApi.getUserPermissions).mockResolvedValue(
        userPermissions
      )

      const wrapper = createWrapper()

      // Act
      const { result } = renderHook(() => useUserPermissions(userId), {
        wrapper,
      })

      // Assert
      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(userPermissions)
      expect(authorizationApi.getUserPermissions).toHaveBeenCalledWith(userId)
    })
  })

  describe("useHasPermission", () => {
    it("should check current user permission", async () => {
      // Arrange
      const resource = "project"
      const action = "read"

      vi.mocked(authStore.useAuthStore).mockReturnValue({
        user: mockUser,
        isAuthenticated: true,
        token: "mock-token",
        isLoading: false,
        lastActivity: null,
        setAuth: vi.fn(),
        updateUser: vi.fn(),
        clearAuth: vi.fn(),
        setLoading: vi.fn(),
        initializeAuth: vi.fn(),
      })
      vi.mocked(authorizationApi.checkUserPermission).mockResolvedValue(true)

      const wrapper = createWrapper()

      // Act
      const { result } = renderHook(() => useHasPermission(resource, action), {
        wrapper,
      })

      // Assert
      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toBe(true)
      expect(authorizationApi.checkUserPermission).toHaveBeenCalledWith(
        mockUser.id,
        resource,
        action
      )
    })

    it("should handle user without ID gracefully", () => {
      // Arrange
      vi.mocked(authStore.useAuthStore).mockReturnValue({
        user: null,
        isAuthenticated: false,
        token: null,
        isLoading: false,
        lastActivity: null,
        setAuth: vi.fn(),
        updateUser: vi.fn(),
        clearAuth: vi.fn(),
        setLoading: vi.fn(),
        initializeAuth: vi.fn(),
      })

      const wrapper = createWrapper()

      // Act
      const { result } = renderHook(() => useHasPermission("project", "read"), {
        wrapper,
      })

      // Assert
      expect(result.current.isLoading).toBe(false)
      expect(authorizationApi.checkUserPermission).not.toHaveBeenCalled()
    })
  })

  describe("useCurrentUserPermissions", () => {
    it("should fetch current user permissions", async () => {
      // Arrange
      const userPermissions = ["project.read", "project.write"]

      vi.mocked(authStore.useAuthStore).mockReturnValue({
        user: mockUser,
        isAuthenticated: true,
        token: "mock-token",
        isLoading: false,
        lastActivity: null,
        setAuth: vi.fn(),
        updateUser: vi.fn(),
        clearAuth: vi.fn(),
        setLoading: vi.fn(),
        initializeAuth: vi.fn(),
      })
      vi.mocked(authorizationApi.getUserPermissions).mockResolvedValue(
        userPermissions
      )

      const wrapper = createWrapper()

      // Act
      const { result } = renderHook(() => useCurrentUserPermissions(), {
        wrapper,
      })

      // Assert
      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(userPermissions)
      expect(authorizationApi.getUserPermissions).toHaveBeenCalledWith(
        mockUser.id
      )
    })
  })

  describe("Hook caching behavior", () => {
    it("should cache roles data with appropriate stale time", async () => {
      // Arrange
      vi.mocked(authStore.useAuthStore).mockReturnValue({
        isAuthenticated: true,
        user: mockUser,
        token: "mock-token",
        isLoading: false,
        lastActivity: null,
        setAuth: vi.fn(),
        updateUser: vi.fn(),
        clearAuth: vi.fn(),
        setLoading: vi.fn(),
        initializeAuth: vi.fn(),
      })
      vi.mocked(authorizationApi.getAllRoles).mockResolvedValue(mockRoles)

      const wrapper = createWrapper()

      // Act - First render
      const { result, rerender } = renderHook(() => useRoles(), { wrapper })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      // Reset mock call count
      vi.mocked(authorizationApi.getAllRoles).mockClear()

      // Act - Re-render (should use cache)
      rerender()

      // Assert - Should not make another API call due to stale time
      expect(authorizationApi.getAllRoles).not.toHaveBeenCalled()
      expect(result.current.data).toEqual(mockRoles)
    })

    it("should cache permission checks with appropriate stale time", async () => {
      // Arrange
      vi.mocked(authStore.useAuthStore).mockReturnValue({
        isAuthenticated: true,
        user: mockUser,
        token: "mock-token",
        isLoading: false,
        lastActivity: null,
        setAuth: vi.fn(),
        updateUser: vi.fn(),
        clearAuth: vi.fn(),
        setLoading: vi.fn(),
        initializeAuth: vi.fn(),
      })
      vi.mocked(authorizationApi.checkUserPermission).mockResolvedValue(true)

      const wrapper = createWrapper()

      // Act - First render
      const { result, rerender } = renderHook(
        () => useUserPermission(1, "project", "read"),
        { wrapper }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      // Reset mock call count
      vi.mocked(authorizationApi.checkUserPermission).mockClear()

      // Act - Re-render (should use cache)
      rerender()

      // Assert - Should not make another API call due to stale time
      expect(authorizationApi.checkUserPermission).not.toHaveBeenCalled()
      expect(result.current.data).toBe(true)
    })
  })

  describe("Error handling", () => {
    it("should handle network errors gracefully", async () => {
      // Arrange
      vi.mocked(authStore.useAuthStore).mockReturnValue({
        isAuthenticated: true,
        user: mockUser,
        token: "mock-token",
        isLoading: false,
        lastActivity: null,
        setAuth: vi.fn(),
        updateUser: vi.fn(),
        clearAuth: vi.fn(),
        setLoading: vi.fn(),
        initializeAuth: vi.fn(),
      })
      vi.mocked(authorizationApi.getAllRoles).mockRejectedValue(
        new Error("Network Error")
      )

      const wrapper = createWrapper()

      // Act
      const { result } = renderHook(() => useRoles(), { wrapper })

      // Assert
      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error).toEqual(new Error("Network Error"))
      expect(result.current.data).toBeUndefined()
    })

    it("should handle permission denied errors", async () => {
      // Arrange
      vi.mocked(authStore.useAuthStore).mockReturnValue({
        isAuthenticated: true,
        user: mockUser,
        token: "mock-token",
        isLoading: false,
        lastActivity: null,
        setAuth: vi.fn(),
        updateUser: vi.fn(),
        clearAuth: vi.fn(),
        setLoading: vi.fn(),
        initializeAuth: vi.fn(),
      })
      vi.mocked(authorizationApi.checkUserPermission).mockRejectedValue(
        new Error("Permission Denied")
      )

      const wrapper = createWrapper()

      // Act
      const { result } = renderHook(
        () => useUserPermission(1, "admin", "manage"),
        { wrapper }
      )

      // Assert
      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error).toEqual(new Error("Permission Denied"))
    })
  })
})
