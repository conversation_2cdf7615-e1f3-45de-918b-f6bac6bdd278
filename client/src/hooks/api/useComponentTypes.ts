/**
 * Component Type Management React Query Hooks
 */

import type {
  ComponentTypeCreate,
  ComponentTypeListParams,
  ComponentTypeRead,
  ComponentTypeSpecificationTemplate,
  ComponentTypeUpdate,
} from "../../lib/api/types/components"

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"

import { componentTypesApi } from "../../lib/api/endpoints"
import { MutationKeys, QueryKeys } from "../../lib/api/keys"

export function useComponentTypes(params?: ComponentTypeListParams) {
  return useQuery({
    queryKey: QueryKeys.componentTypes.list(params as Record<string, unknown>),
    queryFn: () => componentTypesApi.list(params),
    staleTime: 10 * 60 * 1000,
  })
}

export function useComponentType(id: number) {
  return useQuery({
    queryKey: QueryKeys.componentTypes.detail(id),
    queryFn: () => componentTypesApi.get(id),
    enabled: !!id,
    staleTime: 15 * 60 * 1000,
  })
}

export function useCreateComponentType() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.componentTypes.create,
    mutationFn: (data: ComponentTypeCreate) => componentTypesApi.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.componentTypes.all })
    },
  })
}

export function useUpdateComponentType() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["componentTypes", "update"] as const,
    mutationFn: ({ id, data }: { id: number; data: ComponentTypeUpdate }) =>
      componentTypesApi.update(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.componentTypes.detail(id),
      })
      queryClient.invalidateQueries({ queryKey: QueryKeys.componentTypes.all })
    },
  })
}

export function useDeleteComponentType() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["componentTypes", "delete"] as const,
    mutationFn: (id: number) => componentTypesApi.delete(id),
    onSuccess: (_, id) => {
      queryClient.removeQueries({
        queryKey: QueryKeys.componentTypes.detail(id),
      })
      queryClient.invalidateQueries({ queryKey: QueryKeys.componentTypes.all })
    },
  })
}

export function useComponentTypesByCategory(
  categoryId: number,
  params?: ComponentTypeListParams
) {
  return useQuery({
    queryKey: QueryKeys.componentTypes.byCategory(
      categoryId,
      params as Record<string, unknown>
    ),
    queryFn: () => componentTypesApi.getByCategory(categoryId, params),
    enabled: !!categoryId,
    staleTime: 10 * 60 * 1000,
  })
}

export function useComponentTypeSpecificationTemplate(id: number) {
  return useQuery({
    queryKey: QueryKeys.componentTypes.specificationTemplate(id),
    queryFn: () => componentTypesApi.getSpecificationTemplate(id),
    enabled: !!id,
    staleTime: 15 * 60 * 1000,
  })
}

export function useUpdateComponentTypeSpecificationTemplate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["componentTypes", "updateSpecificationTemplate"] as const,
    mutationFn: ({
      id,
      template,
    }: {
      id: number
      template: ComponentTypeSpecificationTemplate
    }) => componentTypesApi.updateSpecificationTemplate(id, template),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.componentTypes.specificationTemplate(id),
      })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.componentTypes.detail(id),
      })
    },
  })
}

export function useComponentTypeStats() {
  return useQuery({
    queryKey: QueryKeys.componentTypes.stats,
    queryFn: () => componentTypesApi.getStats(),
    staleTime: 15 * 60 * 1000,
  })
}

export function useBulkCreateComponentTypes() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.componentTypes.bulkCreate,
    mutationFn: (types: ComponentTypeCreate[]) =>
      componentTypesApi.bulkCreate(types),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.componentTypes.all })
    },
  })
}

export function useImportComponentTypes() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.componentTypes.import,
    mutationFn: (
      data: Array<{
        name: string
        description?: string
        category_name: string
        specifications_template?: ComponentTypeSpecificationTemplate
      }>
    ) => componentTypesApi.import(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.componentTypes.all })
    },
  })
}
