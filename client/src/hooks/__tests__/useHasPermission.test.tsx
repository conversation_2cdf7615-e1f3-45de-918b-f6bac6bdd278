/**
 * Comprehensive test suite for useHasPermission hook
 *
 * Tests permission checking logic, HOC functionality,
 * and multi-permission utilities.
 */

import { ReactNode } from "react"

import * as authStore from "@/stores/authStore"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { render, renderHook, screen, waitFor } from "@testing-library/react"
import { beforeEach, describe, expect, it, vi } from "vitest"

import * as useAuthorizationHook from "@/hooks/api/useAuthorization"
import * as authorizationApi from "@/lib/api/endpoints/authorization"

import {
  useHasMultiplePermissions,
  useHasPermission,
  withPermission,
} from "../useHasPermission"

// Mock the auth store
vi.mock("@/stores/authStore", () => ({
  useAuthStore: vi.fn(),
}))

// Mock the authorization hook
vi.mock("@/hooks/api/useAuthorization", () => ({
  useHasPermission: vi.fn(),
}))

// Mock the authorization API
vi.mock("@/lib/api/endpoints/authorization", () => ({
  authorizationApi: {
    checkUserPermission: vi.fn(),
  },
}))

// Test data
const mockUser = {
  id: 1,
  name: "Test User",
  email: "<EMAIL>",
  is_superuser: false,
  is_active: true,
  permissions: ["project.read", "component.read"],
  created_at: "2024-01-01T00:00:00Z",
  updated_at: "2024-01-01T00:00:00Z",
}

const mockSuperuser = {
  id: 2,
  name: "Super User",
  email: "<EMAIL>",
  is_superuser: true,
  is_active: true,
  permissions: [],
  created_at: "2024-01-01T00:00:00Z",
  updated_at: "2024-01-01T00:00:00Z",
}

// Test wrapper with QueryClient
function createWrapper() {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  const Wrapper = ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
  Wrapper.displayName = "QueryClientTestWrapper"
  return Wrapper
}

// Mock React component for HOC testing
function TestComponent({ text = "Protected Content" }: { text?: string }) {
  return <div>{text}</div>
}

describe("useHasPermission", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe("Basic permission checking", () => {
    it("should return false when user is not authenticated", () => {
      // Arrange
      vi.mocked(authStore.useAuthStore).mockReturnValue({
        user: null,
        isAuthenticated: false,
        token: null,
        isLoading: false,
        lastActivity: null,
        setAuth: vi.fn(),
        updateUser: vi.fn(),
        clearAuth: vi.fn(),
        setLoading: vi.fn(),
        initializeAuth: vi.fn(),
      })

      const wrapper = createWrapper()

      // Act
      const { result } = renderHook(
        () =>
          useHasPermission({
            resource: "project",
            action: "read",
          }),
        { wrapper }
      )

      // Assert
      expect(result.current.hasPermission).toBe(false)
      expect(result.current.isAuthenticated).toBe(false)
      expect(result.current.isSuperuser).toBe(false)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.isError).toBe(false)
    })

    it("should return true for superuser regardless of permission", () => {
      // Arrange
      vi.mocked(authStore.useAuthStore).mockReturnValue({
        user: mockSuperuser,
        isAuthenticated: true,
        token: "mock-token",
        isLoading: false,
        lastActivity: null,
        setAuth: vi.fn(),
        updateUser: vi.fn(),
        clearAuth: vi.fn(),
        setLoading: vi.fn(),
        initializeAuth: vi.fn(),
      })

      const wrapper = createWrapper()

      // Act
      const { result } = renderHook(
        () =>
          useHasPermission({
            resource: "any_resource",
            action: "any_action",
          }),
        { wrapper }
      )

      // Assert
      expect(result.current.hasPermission).toBe(true)
      expect(result.current.isAuthenticated).toBe(true)
      expect(result.current.isSuperuser).toBe(true)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.isError).toBe(false)
    })

    it("should return false for superuser when superuser access is disabled", () => {
      // Arrange
      vi.mocked(authStore.useAuthStore).mockReturnValue({
        user: mockSuperuser,
        isAuthenticated: true,
        token: "mock-token",
        isLoading: false,
        lastActivity: null,
        setAuth: vi.fn(),
        updateUser: vi.fn(),
        clearAuth: vi.fn(),
        setLoading: vi.fn(),
        initializeAuth: vi.fn(),
      })

      vi.mocked(useAuthorizationHook.useHasPermission).mockReturnValue({
        data: false,
        isLoading: false,
        isError: false,
        error: null,
      } as any)

      const wrapper = createWrapper()

      // Act
      const { result } = renderHook(
        () =>
          useHasPermission({
            resource: "admin",
            action: "manage",
            allowSuperuser: false,
          }),
        { wrapper }
      )

      // Assert
      expect(result.current.hasPermission).toBe(false)
      expect(result.current.isSuperuser).toBe(true)
    })

    it("should return true when user has cached permission", () => {
      // Arrange
      vi.mocked(authStore.useAuthStore).mockReturnValue({
        user: mockUser,
        isAuthenticated: true,
        token: "mock-token",
        isLoading: false,
        lastActivity: null,
        setAuth: vi.fn(),
        updateUser: vi.fn(),
        clearAuth: vi.fn(),
        setLoading: vi.fn(),
        initializeAuth: vi.fn(),
      })

      const wrapper = createWrapper()

      // Act
      const { result } = renderHook(
        () =>
          useHasPermission({
            resource: "project",
            action: "read", // User has 'project.read' in cached permissions
          }),
        { wrapper }
      )

      // Assert
      expect(result.current.hasPermission).toBe(true)
      expect(result.current.isAuthenticated).toBe(true)
      expect(result.current.isSuperuser).toBe(false)
    })

    it("should fallback to server check when permission not in cache", () => {
      // Arrange
      vi.mocked(authStore.useAuthStore).mockReturnValue({
        user: mockUser,
        isAuthenticated: true,
        token: "mock-token",
        isLoading: false,
        lastActivity: null,
        setAuth: vi.fn(),
        updateUser: vi.fn(),
        clearAuth: vi.fn(),
        setLoading: vi.fn(),
        initializeAuth: vi.fn(),
      })

      vi.mocked(useAuthorizationHook.useHasPermission).mockReturnValue({
        data: true,
        isLoading: false,
        isError: false,
        error: null,
      } as any)

      const wrapper = createWrapper()

      // Act
      const { result } = renderHook(
        () =>
          useHasPermission({
            resource: "project",
            action: "write", // Not in cached permissions
          }),
        { wrapper }
      )

      // Assert
      expect(result.current.hasPermission).toBe(true)
      expect(useAuthorizationHook.useHasPermission).toHaveBeenCalledWith(
        "project",
        "write"
      )
    })

    it("should return fallback value when specified", () => {
      // Arrange
      vi.mocked(authStore.useAuthStore).mockReturnValue({
        user: null,
        isAuthenticated: false,
        token: null,
        isLoading: false,
        lastActivity: null,
        setAuth: vi.fn(),
        updateUser: vi.fn(),
        clearAuth: vi.fn(),
        setLoading: vi.fn(),
        initializeAuth: vi.fn(),
      })

      const wrapper = createWrapper()

      // Act
      const { result } = renderHook(
        () =>
          useHasPermission({
            resource: "project",
            action: "read",
            fallback: true,
          }),
        { wrapper }
      )

      // Assert
      expect(result.current.hasPermission).toBe(true) // fallback value
    })

    it("should handle server-side loading state", () => {
      // Arrange
      vi.mocked(authStore.useAuthStore).mockReturnValue({
        user: mockUser,
        isAuthenticated: true,
        token: "mock-token",
        isLoading: false,
        lastActivity: null,
        setAuth: vi.fn(),
        updateUser: vi.fn(),
        clearAuth: vi.fn(),
        setLoading: vi.fn(),
        initializeAuth: vi.fn(),
      })

      vi.mocked(useAuthorizationHook.useHasPermission).mockReturnValue({
        data: undefined,
        isLoading: true,
        isError: false,
        error: null,
      } as any)

      const wrapper = createWrapper()

      // Act
      const { result } = renderHook(
        () =>
          useHasPermission({
            resource: "project",
            action: "write",
          }),
        { wrapper }
      )

      // Assert
      expect(result.current.hasPermission).toBe(false)
      expect(result.current.isLoading).toBe(true)
    })

    it("should handle server-side errors", () => {
      // Arrange
      const mockError = new Error("Permission check failed")
      vi.mocked(authStore.useAuthStore).mockReturnValue({
        user: mockUser,
        isAuthenticated: true,
        token: "mock-token",
        isLoading: false,
        lastActivity: null,
        setAuth: vi.fn(),
        updateUser: vi.fn(),
        clearAuth: vi.fn(),
        setLoading: vi.fn(),
        initializeAuth: vi.fn(),
      })

      vi.mocked(useAuthorizationHook.useHasPermission).mockReturnValue({
        data: undefined,
        isLoading: false,
        isError: true,
        error: mockError,
      } as any)

      const wrapper = createWrapper()

      // Act
      const { result } = renderHook(
        () =>
          useHasPermission({
            resource: "project",
            action: "write",
          }),
        { wrapper }
      )

      // Assert
      expect(result.current.hasPermission).toBe(false)
      expect(result.current.isError).toBe(true)
      expect(result.current.error).toBe(mockError)
    })
  })

  describe("Memoization behavior", () => {
    it("should memoize results to avoid unnecessary re-renders", () => {
      // Arrange
      vi.mocked(authStore.useAuthStore).mockReturnValue({
        user: mockUser,
        isAuthenticated: true,
        token: "mock-token",
        isLoading: false,
        lastActivity: null,
        setAuth: vi.fn(),
        updateUser: vi.fn(),
        clearAuth: vi.fn(),
        setLoading: vi.fn(),
        initializeAuth: vi.fn(),
      })

      const wrapper = createWrapper()

      // Act
      const { result, rerender } = renderHook(
        () =>
          useHasPermission({
            resource: "project",
            action: "read",
          }),
        { wrapper }
      )

      const firstResult = result.current
      rerender()
      const secondResult = result.current

      // Assert - Results should be referentially equal due to memoization
      expect(firstResult).toBe(secondResult)
    })
  })
})

describe("withPermission HOC", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it("should render component when user has permission", () => {
    // Arrange
    vi.mocked(authStore.useAuthStore).mockReturnValue({
      user: mockUser,
      isAuthenticated: true,
      token: "mock-token",
      isLoading: false,
      lastActivity: null,
      setAuth: vi.fn(),
      updateUser: vi.fn(),
      clearAuth: vi.fn(),
      setLoading: vi.fn(),
      initializeAuth: vi.fn(),
    })

    vi.mocked(useAuthorizationHook.useHasPermission).mockReturnValue({
      data: true,
      isLoading: false,
      isError: false,
      error: null,
    } as any)

    const ProtectedComponent = withPermission("project", "read")(TestComponent)

    // Act
    render(
      <QueryClientProvider client={new QueryClient()}>
        <ProtectedComponent text="Protected Content" />
      </QueryClientProvider>
    )

    // Assert
    expect(screen.getByText("Protected Content")).toBeInTheDocument()
  })

  it("should not render component when user lacks permission", () => {
    // Arrange
    vi.mocked(authStore.useAuthStore).mockReturnValue({
      user: mockUser,
      isAuthenticated: true,
      token: "mock-token",
      isLoading: false,
      lastActivity: null,
      setAuth: vi.fn(),
      updateUser: vi.fn(),
      clearAuth: vi.fn(),
      setLoading: vi.fn(),
      initializeAuth: vi.fn(),
    })

    vi.mocked(useAuthorizationHook.useHasPermission).mockReturnValue({
      data: false,
      isLoading: false,
      isError: false,
      error: null,
    } as any)

    const ProtectedComponent = withPermission("admin", "manage")(TestComponent)

    // Act
    render(
      <QueryClientProvider client={new QueryClient()}>
        <ProtectedComponent text="Protected Content" />
      </QueryClientProvider>
    )

    // Assert
    expect(screen.queryByText("Protected Content")).not.toBeInTheDocument()
  })

  it("should not render component while loading", () => {
    // Arrange
    vi.mocked(authStore.useAuthStore).mockReturnValue({
      user: mockUser,
      isAuthenticated: true,
      token: "mock-token",
      isLoading: false,
      lastActivity: null,
      setAuth: vi.fn(),
      updateUser: vi.fn(),
      clearAuth: vi.fn(),
      setLoading: vi.fn(),
      initializeAuth: vi.fn(),
    })

    vi.mocked(useAuthorizationHook.useHasPermission).mockReturnValue({
      data: undefined,
      isLoading: true,
      isError: false,
      error: null,
    } as any)

    // Use a permission NOT in the user's cached permissions
    const ProtectedComponent = withPermission("admin", "manage")(TestComponent)

    // Act
    render(
      <QueryClientProvider client={new QueryClient()}>
        <ProtectedComponent text="Protected Content" />
      </QueryClientProvider>
    )

    // Assert
    expect(screen.queryByText("Protected Content")).not.toBeInTheDocument()
  })

  it("should pass through component props correctly", () => {
    // Arrange
    vi.mocked(authStore.useAuthStore).mockReturnValue({
      user: mockUser,
      isAuthenticated: true,
      token: "mock-token",
      isLoading: false,
      lastActivity: null,
      setAuth: vi.fn(),
      updateUser: vi.fn(),
      clearAuth: vi.fn(),
      setLoading: vi.fn(),
      initializeAuth: vi.fn(),
    })

    vi.mocked(useAuthorizationHook.useHasPermission).mockReturnValue({
      data: true,
      isLoading: false,
      isError: false,
      error: null,
    } as any)

    const ProtectedComponent = withPermission("project", "read")(TestComponent)

    // Act
    render(
      <QueryClientProvider client={new QueryClient()}>
        <ProtectedComponent text="Custom Text" />
      </QueryClientProvider>
    )

    // Assert
    expect(screen.getByText("Custom Text")).toBeInTheDocument()
  })

  it("should accept additional options", () => {
    // Arrange
    vi.mocked(authStore.useAuthStore).mockReturnValue({
      user: mockSuperuser,
      isAuthenticated: true,
      token: "mock-token",
      isLoading: false,
      lastActivity: null,
      setAuth: vi.fn(),
      updateUser: vi.fn(),
      clearAuth: vi.fn(),
      setLoading: vi.fn(),
      initializeAuth: vi.fn(),
    })

    const ProtectedComponent = withPermission("admin", "manage", {
      allowSuperuser: false,
      fallback: true,
    })(TestComponent)

    // Act
    render(
      <QueryClientProvider client={new QueryClient()}>
        <ProtectedComponent text="Should show due to fallback" />
      </QueryClientProvider>
    )

    // Assert - Should still render due to fallback even though superuser is disabled
    expect(screen.getByText("Should show due to fallback")).toBeInTheDocument()
  })
})

describe("useHasMultiplePermissions", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it("should check multiple permissions and return aggregated results", async () => {
    // Arrange
    vi.mocked(authStore.useAuthStore).mockReturnValue({
      user: mockUser,
      isAuthenticated: true,
      token: "mock-token",
      isLoading: false,
      lastActivity: null,
      setAuth: vi.fn(),
      updateUser: vi.fn(),
      clearAuth: vi.fn(),
      setLoading: vi.fn(),
      initializeAuth: vi.fn(),
    })

    // Mock the API call for "admin.manage" (not cached)
    vi.mocked(authorizationApi.authorizationApi.checkUserPermission).mockResolvedValue(false)

    const wrapper = createWrapper()

    // Act
    const { result } = renderHook(
      () =>
        useHasMultiplePermissions([
          "project.read", // User has this
          "component.read", // User has this
          "admin.manage", // User doesn't have this
        ]),
      { wrapper }
    )

    // Wait for queries to complete
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })

    // Assert
    expect(result.current.results).toHaveLength(3)

    // Check individual results
    const projectRead = result.current.results.find(
      (r) => r.permission === "project.read"
    )
    const componentRead = result.current.results.find(
      (r) => r.permission === "component.read"
    )
    const adminManage = result.current.results.find(
      (r) => r.permission === "admin.manage"
    )

    expect(projectRead?.hasPermission).toBe(true)
    expect(componentRead?.hasPermission).toBe(true)
    expect(adminManage?.hasPermission).toBe(false)

    // Check aggregated results
    expect(result.current.hasAnyPermission).toBe(true) // Has project.read and component.read
    expect(result.current.hasAllPermissions).toBe(false) // Doesn't have admin.manage
    expect(result.current.isLoading).toBe(false)
    expect(result.current.isError).toBe(false)
  })

  it("should handle empty permissions array", () => {
    // Arrange
    vi.mocked(authStore.useAuthStore).mockReturnValue({
      user: mockUser,
      isAuthenticated: true,
      token: "mock-token",
      isLoading: false,
      lastActivity: null,
      setAuth: vi.fn(),
      updateUser: vi.fn(),
      clearAuth: vi.fn(),
      setLoading: vi.fn(),
      initializeAuth: vi.fn(),
    })

    const wrapper = createWrapper()

    // Act
    const { result } = renderHook(() => useHasMultiplePermissions([]), {
      wrapper,
    })

    // Assert
    expect(result.current.results).toHaveLength(0)
    expect(result.current.hasAnyPermission).toBe(false)
    expect(result.current.hasAllPermissions).toBe(true) // Vacuous truth
    expect(result.current.isLoading).toBe(false)
    expect(result.current.isError).toBe(false)
  })

  it("should handle loading states correctly", () => {
    // Arrange
    vi.mocked(authStore.useAuthStore).mockReturnValue({
      user: mockUser,
      isAuthenticated: true,
      token: "mock-token",
      isLoading: false,
      lastActivity: null,
      setAuth: vi.fn(),
      updateUser: vi.fn(),
      clearAuth: vi.fn(),
      setLoading: vi.fn(),
      initializeAuth: vi.fn(),
    })

    vi.mocked(useAuthorizationHook.useHasPermission)
      .mockReturnValueOnce({
        data: true,
        isLoading: false,
        isError: false,
        error: null,
      } as any)
      .mockReturnValueOnce({
        data: undefined,
        isLoading: true,
        isError: false,
        error: null,
      } as any)

    const wrapper = createWrapper()

    // Act
    const { result } = renderHook(
      () => useHasMultiplePermissions(["project.read", "admin.manage"]),
      { wrapper }
    )

    // Assert
    expect(result.current.isLoading).toBe(true) // Any loading means overall loading
    expect(result.current.isError).toBe(false)
  })

  it("should handle error states correctly", async () => {
    // Arrange
    const mockError = new Error("Permission check failed")
    vi.mocked(authStore.useAuthStore).mockReturnValue({
      user: mockUser,
      isAuthenticated: true,
      token: "mock-token",
      isLoading: false,
      lastActivity: null,
      setAuth: vi.fn(),
      updateUser: vi.fn(),
      clearAuth: vi.fn(),
      setLoading: vi.fn(),
      initializeAuth: vi.fn(),
    })

    // Mock the API call for "admin.manage" to throw error
    vi.mocked(authorizationApi.authorizationApi.checkUserPermission).mockRejectedValue(mockError)

    const wrapper = createWrapper()

    // Act
    const { result } = renderHook(
      () => useHasMultiplePermissions(["project.read", "admin.manage"]),
      { wrapper }
    )

    // Wait for queries to complete (error state)
    await waitFor(() => {
      expect(result.current.isError).toBe(true)
    })

    // Assert
    expect(result.current.isError).toBe(true) // Any error means overall error
    expect(result.current.isLoading).toBe(false)
  })

  it("should pass through additional options to individual permission checks", () => {
    // Arrange
    vi.mocked(authStore.useAuthStore).mockReturnValue({
      user: mockSuperuser,
      isAuthenticated: true,
      token: "mock-token",
      isLoading: false,
      lastActivity: null,
      setAuth: vi.fn(),
      updateUser: vi.fn(),
      clearAuth: vi.fn(),
      setLoading: vi.fn(),
      initializeAuth: vi.fn(),
    })

    const wrapper = createWrapper()

    // Act
    const { result } = renderHook(
      () =>
        useHasMultiplePermissions(["admin.manage", "user.delete"], {
          allowSuperuser: false,
        }),
      { wrapper }
    )

    // Assert - All permissions should be false since superuser is disabled
    expect(result.current.results.every((r) => r.hasPermission === false)).toBe(
      true
    )
    expect(result.current.hasAllPermissions).toBe(false)
    expect(result.current.hasAnyPermission).toBe(false)
  })

  it("should parse permission strings correctly", () => {
    // Arrange
    vi.mocked(authStore.useAuthStore).mockReturnValue({
      user: mockUser,
      isAuthenticated: true,
      token: "mock-token",
      isLoading: false,
      lastActivity: null,
      setAuth: vi.fn(),
      updateUser: vi.fn(),
      clearAuth: vi.fn(),
      setLoading: vi.fn(),
      initializeAuth: vi.fn(),
    })

    const wrapper = createWrapper()

    // Act
    const { result } = renderHook(
      () => useHasMultiplePermissions(["project.read", "component.write"]),
      { wrapper }
    )

    // Assert
    const projectPermission = result.current.results.find(
      (r) => r.permission === "project.read"
    )
    const componentPermission = result.current.results.find(
      (r) => r.permission === "component.write"
    )

    expect(projectPermission?.resource).toBe("project")
    expect(projectPermission?.action).toBe("read")
    expect(componentPermission?.resource).toBe("component")
    expect(componentPermission?.action).toBe("write")
  })
})
