import { useAuthStore } from "@/stores/authStore"

import {
  useCurrentUser,
  useLogin,
  useLogout,
  useRegister,
} from "@/hooks/api/useAuth"

/**
 * Compatibility wrapper hook for tests that still import '@/hooks/useAuth'
 * Provides a simplified interface delegating to the new API hooks + store.
 */
export function useAuth() {
  const store = useAuthStore()
  const loginMutation = useLogin()
  const logoutMutation = useLogout()
  const currentUserQuery = useCurrentUser()

  return {
    // State
    isAuthenticated: store.isAuthenticated,
    isLoading: store.isLoading || currentUserQuery.isLoading,
    user: store.user,
    token: store.token,

    // Actions
    login: async (credentials: { username: string; password: string }) => {
      const res = await loginMutation.mutateAsync(credentials as any)
      return res
    },
    logout: async () => {
      await logoutMutation.mutateAsync()
    },

    // Error states
    loginError: (loginMutation.error as any) ?? null,
    logoutError: (logoutMutation.error as any) ?? null,

    // Derived helpers
    isLoginPending: loginMutation.isPending,
    isLogoutPending: logoutMutation.isPending,

    isAdmin: () => !!store.user?.is_superuser,
    hasRole: (role: string) => store.user?.role === role,

    requireAuth: () => {
      if (!store.isAuthenticated) throw new Error("Not authenticated")
    },
    requireAdmin: () => {
      if (!store.user?.is_superuser) throw new Error("Admin required")
    },
  }
}
