/**
 * useOfflineMutation Hook - Offline-first mutation handling
 *
 * This hook extends React Query's useMutation to provide offline-first functionality.
 * When offline, mutations are stored in IndexedDB's mutation-outbox for later synchronization.
 * When online, mutations are executed immediately through React Query.
 *
 * Features:
 * - Automatic online/offline detection
 * - IndexedDB persistence for offline mutations
 * - Type-safe mutation record serialization
 * - Error-resilient with graceful degradation
 * - Compatible with React Query patterns
 */

import { useCallback, useEffect, useState } from "react"

import type {
  UseMutationOptions,
  UseMutationResult,
} from "@tanstack/react-query"

import { CacheStats, createIndexedDBPersister } from "@/core/caching"
import { useMutation, useQueryClient } from "@tanstack/react-query"

/**
 * Offline mutation record stored in IndexedDB mutation-outbox
 */
export interface OfflineMutationRecord {
  /** Auto-incrementing ID (set by IndexedDB) */
  id?: number
  /** API endpoint URL */
  endpoint: string
  /** HTTP method (GET, POST, PUT, DELETE, etc.) */
  method: string
  /** Serialized mutation payload/variables */
  payload: any
  /** ISO timestamp when mutation was created */
  timestamp: string
  /** Unique identifier for mutation deduplication */
  mutationKey: string
  /** Number of retry attempts */
  retryCount?: number
  /** Last error message if any */
  lastError?: string
  /** Mutation options serialized for retry */
  options?: {
    onSuccess?: string // Serialized function identifier
    onError?: string // Serialized function identifier
    retry?: number
    retryDelay?: number
  }
}

/**
 * Configuration for useOfflineMutation hook
 */
export interface UseOfflineMutationConfig<TVariables> {
  /** API endpoint URL */
  endpoint: string
  /** HTTP method */
  method: string
  /** Unique mutation key for deduplication */
  mutationKey: string
  /** Custom IndexedDB database name */
  dbName?: string
  /** Enable detailed logging */
  enableLogging?: boolean
  /** Custom online/offline detection */
  isOnline?: () => boolean
  /** Transform variables before storage */
  transformVariables?: (variables: TVariables) => any
  /** Transform stored payload back to variables */
  restoreVariables?: (payload: any) => TVariables
}

/**
 * Extended mutation result with offline-specific properties
 */
export interface OfflineMutationResult<TData, TError, TVariables> {
  /** Whether the last mutation was stored offline */
  wasStoredOffline: boolean
  /** Number of pending offline mutations for this mutation key */
  pendingOfflineCount: number
  /** Get statistics about offline mutations */
  getOfflineStats: () => Promise<CacheStats>
  /** Clear all pending offline mutations for this mutation key */
  clearOfflineMutations: () => Promise<void>
}

/**
 * Mutation outbox manager for IndexedDB operations
 */
class MutationOutboxManager {
  private dbName: string

  constructor(dbName: string = "ultimate-electrical-designer-cache") {
    this.dbName = dbName
    createIndexedDBPersister({
      dbName,
      dbVersion: 1,
      storeName: "mutation-outbox",
      cacheKey: "offline-mutations",
    })
  }

  /**
   * Store a mutation in the offline outbox
   */
  async storeMutation(
    record: Omit<OfflineMutationRecord, "id">
  ): Promise<number> {
    try {
      const db = await this.getDatabase()
      const transaction = db.transaction(["mutation-outbox"], "readwrite")
      const store = transaction.objectStore("mutation-outbox")

      const result = await store.add(record)
      await transaction.done

      console.log(
        `[OfflineMutation] Stored mutation ${record.mutationKey} with ID ${result}`
      )
      return result as number
    } catch (error) {
      console.error(
        "[OfflineMutation] Failed to store offline mutation:",
        error
      )
      throw error
    }
  }

  /**
   * Get all pending mutations for a specific mutation key
   */
  async getPendingMutations(
    mutationKey?: string
  ): Promise<OfflineMutationRecord[]> {
    try {
      const db = await this.getDatabase()
      const transaction = db.transaction(["mutation-outbox"], "readonly")
      const store = transaction.objectStore("mutation-outbox")

      let records: OfflineMutationRecord[]

      if (mutationKey) {
        // Use index to filter by mutation key if available
        const index = store.index?.("mutationKey")
        records = index ? await index.getAll(mutationKey) : await store.getAll()
        if (!index) {
          records = records.filter(
            (record) => record.mutationKey === mutationKey
          )
        }
      } else {
        records = await store.getAll()
      }

      // Sort by timestamp (oldest first)
      return records.sort(
        (a, b) =>
          new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
      )
    } catch (error) {
      console.error("[OfflineMutation] Failed to get pending mutations:", error)
      return []
    }
  }

  /**
   * Remove a mutation from the outbox
   */
  async removeMutation(id: number): Promise<void> {
    try {
      const db = await this.getDatabase()
      const transaction = db.transaction(["mutation-outbox"], "readwrite")
      const store = transaction.objectStore("mutation-outbox")

      await store.delete(id)
      await transaction.done

      console.log(`[OfflineMutation] Removed mutation with ID ${id}`)
    } catch (error) {
      console.error("[OfflineMutation] Failed to remove mutation:", error)
      throw error
    }
  }

  /**
   * Clear all mutations for a specific mutation key
   */
  async clearMutations(mutationKey: string): Promise<void> {
    try {
      const mutations = await this.getPendingMutations(mutationKey)
      const db = await this.getDatabase()
      const transaction = db.transaction(["mutation-outbox"], "readwrite")
      const store = transaction.objectStore("mutation-outbox")

      for (const mutation of mutations) {
        if (mutation.id) {
          await store.delete(mutation.id)
        }
      }

      await transaction.done
      console.log(
        `[OfflineMutation] Cleared ${mutations.length} mutations for key ${mutationKey}`
      )
    } catch (error) {
      console.error("[OfflineMutation] Failed to clear mutations:", error)
      throw error
    }
  }

  /**
   * Get outbox statistics
   */
  async getStats(): Promise<CacheStats> {
    try {
      const db = await this.getDatabase()
      const transaction = db.transaction(["mutation-outbox"], "readonly")
      const store = transaction.objectStore("mutation-outbox")

      const count = await store.count()
      const allRecords = await store.getAll()

      let lastUpdated: number | undefined
      if (allRecords.length > 0) {
        const latestTimestamp = Math.max(
          ...allRecords.map((r: any) => new Date(r.timestamp).getTime())
        )
        lastUpdated = latestTimestamp
      }

      return {
        isSupported: true,
        cacheSize: count,
        lastUpdated,
        outboxCount: count,
      }
    } catch (error) {
      console.error("[OfflineMutation] Failed to get stats:", error)
      return {
        isSupported: false,
        cacheSize: 0,
        lastUpdated: undefined,
        outboxCount: 0,
      }
    }
  }

  /**
   * Get IndexedDB database instance
   */
  private async getDatabase(): Promise<any> {
    // Use the same database initialization as IndexedDBPersister
    const { openDB } = await import("idb")

    return openDB(this.dbName, 1, {
      upgrade(db) {
        // Create mutation-outbox store if it doesn't exist
        if (!db.objectStoreNames.contains("mutation-outbox")) {
          const outboxStore = db.createObjectStore("mutation-outbox", {
            keyPath: "id",
            autoIncrement: true,
          })

          // Create indexes for efficient querying
          outboxStore.createIndex("timestamp", "timestamp")
          outboxStore.createIndex("mutationKey", "mutationKey")
          outboxStore.createIndex("endpoint", "endpoint")
        }
      },
    })
  }
}

/**
 * Global outbox manager instance
 */
const globalOutboxManager = new MutationOutboxManager()

/**
 * Hook for offline-first mutations
 *
 * @param mutationFn - The mutation function to execute
 * @param config - Configuration for offline behavior
 * @param options - Standard React Query mutation options
 * @returns Extended mutation result with offline capabilities
 */
export function useOfflineMutation<
  TData = unknown,
  TError = Error,
  TVariables = unknown,
>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  config: UseOfflineMutationConfig<TVariables>,
  options?: UseMutationOptions<TData, TError, TVariables>
): OfflineMutationResult<TData, TError, TVariables> {
  useQueryClient() // Available for future use
  const [wasStoredOffline, setWasStoredOffline] = useState(false)
  const [pendingOfflineCount, setPendingOfflineCount] = useState(0)

  // Update pending count when component mounts and periodically
  useEffect(() => {
    const updatePendingCount = async () => {
      try {
        const outboxManager = new MutationOutboxManager(config.dbName)
        const pending = await outboxManager.getPendingMutations(
          config.mutationKey
        )
        setPendingOfflineCount(pending.length)
      } catch (error) {
        console.error(
          "[useOfflineMutation] Failed to update pending count:",
          error
        )
      }
    }

    updatePendingCount()

    // Update count periodically (every 30 seconds)
    const interval = setInterval(updatePendingCount, 30000)

    return () => clearInterval(interval)
  }, [config.mutationKey, config.dbName])

  // Enhanced mutation function with offline handling
  const enhancedMutationFn = useCallback(
    async (variables: TVariables): Promise<TData> => {
      // Custom online detection or fallback to navigator.onLine
      const isOnline = config.isOnline || (() => navigator.onLine)

      if (config.enableLogging) {
        console.log(
          `[useOfflineMutation] Executing mutation ${config.mutationKey}`,
          { variables, online: isOnline() }
        )
      }

      // If online, execute mutation normally
      if (isOnline()) {
        setWasStoredOffline(false)
        return await mutationFn(variables)
      }

      // If offline, store mutation in outbox
      try {
        const outboxManager = new MutationOutboxManager(config.dbName)

        // Transform variables if needed
        const payload = config.transformVariables
          ? config.transformVariables(variables)
          : variables

        const record: Omit<OfflineMutationRecord, "id"> = {
          endpoint: config.endpoint,
          method: config.method.toUpperCase(),
          payload: JSON.parse(JSON.stringify(payload)), // Deep clone and serialize
          timestamp: new Date().toISOString(),
          mutationKey: config.mutationKey,
          retryCount: 0,
          options: {
            retry: options?.retry as number,
            retryDelay: (options as any)?.retryDelay as number,
          },
        }

        const mutationId = await outboxManager.storeMutation(record)
        setWasStoredOffline(true)

        // Update pending count
        setPendingOfflineCount((prev) => prev + 1)

        if (config.enableLogging) {
          console.log(
            `[useOfflineMutation] Stored offline mutation ${config.mutationKey} with ID ${mutationId}`
          )
        }

        // Return a success response for offline storage
        // This allows the UI to show immediate feedback
        return {
          success: true,
          storedOffline: true,
          mutationId,
          message: "Mutation stored for offline sync",
        } as TData
      } catch (error) {
        console.error(
          "[useOfflineMutation] Failed to store offline mutation:",
          error
        )
        throw new Error("Failed to store offline mutation")
      }
    },
    [mutationFn, config, options]
  )

  // Standard React Query mutation with enhanced function
  const mutation = useMutation({
    mutationFn: enhancedMutationFn,
    ...options,
    onSuccess: (data, variables, context) => {
      if (!wasStoredOffline) {
        // Only call original onSuccess for actual API calls
        options?.onSuccess?.(data, variables, context)
      }
    },
    onError: (error, variables, context) => {
      if (!wasStoredOffline) {
        // Only call original onError for actual API calls
        options?.onError?.(error, variables, context)
      }
    },
  })

  // Utility functions
  const getOfflineStats = useCallback(async (): Promise<CacheStats> => {
    const outboxManager = new MutationOutboxManager(config.dbName)
    return await outboxManager.getStats()
  }, [config.dbName])

  const clearOfflineMutations = useCallback(async (): Promise<void> => {
    const outboxManager = new MutationOutboxManager(config.dbName)
    await outboxManager.clearMutations(config.mutationKey)
    setPendingOfflineCount(0)
  }, [config.mutationKey, config.dbName])

  return {
    ...mutation,
    wasStoredOffline,
    pendingOfflineCount,
    getOfflineStats,
    clearOfflineMutations,
  }
}

/**
 * Export the outbox manager for use by SyncManager
 */
export { MutationOutboxManager, globalOutboxManager }
