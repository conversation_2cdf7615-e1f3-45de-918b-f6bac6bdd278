/**
 * Zod base schemas and helpers
 */

import { z } from "zod"

// Common primitives
export const emailString = z.string().trim().toLowerCase().email()
export const nonEmptyString = z.string().trim().min(1)
export const isoDateString = z.string().min(1)

// Timestamp mixin matching backend TimestampMixin
export const timestampMixinSchema = z.object({
  created_at: isoDateString,
  updated_at: z.string().nullable(),
})

// Pagination info and generic paginated response
export const paginationInfoSchema = z.object({
  page: z.number().int().min(1),
  size: z.number().int().min(1),
  total: z.number().int().min(0),
  pages: z.number().int().min(0),
})

export const paginatedResponseSchema = <T extends z.ZodTypeAny>(item: T) =>
  z.object({
    items: z.array(item),
    pagination: paginationInfoSchema,
  })

// Standard API error shape (client side)
export const apiErrorSchema = z.object({
  message: z.string(),
  code: z.string(),
  details: z.any().optional(),
})

export type PaginationInfo = z.infer<typeof paginationInfoSchema>
