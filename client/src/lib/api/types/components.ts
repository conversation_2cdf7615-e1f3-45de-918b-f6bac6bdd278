/**
 * Component management types
 *
 * These types correspond to the backend schemas defined in:
 * server/src/core/schemas/general/component_schemas.py
 */

import {
  PaginatedResponse,
  StatsResponse,
  TimestampMixin,
  ValidationResult,
} from "./common"

// Component dimensions matching ComponentDimensionsSchema
export interface ComponentDimensions {
  length_mm?: number
  width_mm?: number
  height_mm?: number
  diameter_mm?: number
  weight_kg?: number
}

// Component specifications matching ComponentSpecificationsSchema
export interface ComponentSpecifications {
  electrical?: Record<string, unknown>
  thermal?: Record<string, unknown>
  mechanical?: Record<string, unknown>
  standards_compliance?: string[]
  environmental?: Record<string, unknown>
}

// Base component schema matching ComponentBaseSchema
export interface ComponentBase {
  name: string
  description?: string
  manufacturer?: string
  model_number?: string
  category_id: number
  type_id: number
  specifications?: ComponentSpecifications
  unit_price?: number
  currency?: string
  supplier?: string
  part_number?: string
  weight_kg?: number
  dimensions?: ComponentDimensions
  is_active?: boolean
  is_preferred?: boolean
  stock_status?: string
  version?: string
  metadata?: Record<string, unknown>
}

// Component creation matching ComponentCreateSchema
export interface ComponentCreate extends ComponentBase {
  name: string
  category_id: number
  type_id: number
}

// Component update matching ComponentUpdateSchema
export interface ComponentUpdate {
  name?: string
  description?: string
  manufacturer?: string
  model_number?: string
  category_id?: number
  type_id?: number
  specifications?: ComponentSpecifications
  unit_price?: number
  currency?: string
  supplier?: string
  part_number?: string
  weight_kg?: number
  dimensions?: ComponentDimensions
  is_active?: boolean
  is_preferred?: boolean
  stock_status?: string
  version?: string
  metadata?: Record<string, unknown>
}

// Component read matching ComponentReadSchema
export interface ComponentRead extends ComponentBase, TimestampMixin {
  id: number
  full_name?: string
  display_name?: string
  category_name?: string
  type_name?: string
  category_path?: string
}

// Component summary matching ComponentSummarySchema
export interface ComponentSummary {
  id: number
  name: string
  manufacturer?: string
  model_number?: string
  category_name: string
  type_name: string
  is_active: boolean
  is_preferred: boolean
  stock_status: string
  created_at: string
}

// Search and filtering types
export interface ComponentListParams {
  page?: number
  size?: number
  search?: string
  category_id?: number
  type_id?: number
  manufacturer?: string
  is_active?: boolean
  is_preferred?: boolean
  stock_status?: string
  ordering?: string
}

// Advanced search matching ComponentAdvancedSearchSchema
export enum FilterOperator {
  EQUALS = "equals",
  NOT_EQUALS = "not_equals",
  CONTAINS = "contains",
  NOT_CONTAINS = "not_contains",
  STARTS_WITH = "starts_with",
  ENDS_WITH = "ends_with",
  GREATER_THAN = "greater_than",
  LESS_THAN = "less_than",
  GREATER_THAN_OR_EQUAL = "greater_than_or_equal",
  LESS_THAN_OR_EQUAL = "less_than_or_equal",
  IN = "in",
  NOT_IN = "not_in",
  IS_NULL = "is_null",
  IS_NOT_NULL = "is_not_null",
}

export enum LogicalOperator {
  AND = "and",
  OR = "or",
}

export interface RangeFilter {
  min?: number
  max?: number
}

export interface SpecificationFilter {
  key: string
  operator: FilterOperator
  value?: unknown
  range?: RangeFilter
}

export interface AdvancedFilter {
  field: string
  operator: FilterOperator
  value?: unknown
  range?: RangeFilter
}

export interface ComponentAdvancedSearch {
  query?: string
  filters?: AdvancedFilter[]
  specification_filters?: SpecificationFilter[]
  logical_operator?: LogicalOperator
  category_ids?: number[]
  type_ids?: number[]
  manufacturers?: string[]
  is_active?: boolean
  is_preferred?: boolean
  stock_statuses?: string[]
  sort_by?: string
  sort_order?: "asc" | "desc"
}

export interface ComponentSearchResult extends ComponentRead {
  relevance_score?: number
  match_highlights?: Record<string, string[]>
}

export interface ComponentAdvancedSearchResponse {
  results: ComponentSearchResult[]
  total_results: number
  search_time_ms: number
  facets?: Record<string, Record<string, number>>
}

// Bulk operations matching backend schemas
export interface ComponentBulkCreate {
  components: ComponentCreate[]
  validate_only?: boolean
  skip_duplicates?: boolean
}

export interface ComponentBulkUpdate {
  component_ids: number[]
  updates: ComponentUpdate
}

export interface ComponentValidationResult
  extends ValidationResult<ComponentCreate> {
  duplicate_components?: ComponentCreate[]
}

// Component statistics matching ComponentStatsSchema
export interface ComponentStats extends StatsResponse {
  total_components: number
  active_components: number
  inactive_components: number
  preferred_components: number
  by_category: Record<string, number>
  by_manufacturer: Record<string, number>
  by_stock_status: Record<string, number>
}

// Import/Export types
export interface ComponentImportData {
  name: string
  description?: string
  manufacturer?: string
  model_number?: string
  category_name: string
  type_name: string
  specifications?: Record<string, unknown>
  unit_price?: number
  currency?: string
  supplier?: string
  part_number?: string
  weight_kg?: number
  dimensions?: ComponentDimensions
}

export interface ComponentExportParams {
  format: "csv" | "xlsx" | "json"
  include_inactive?: boolean
  category_ids?: number[]
  type_ids?: number[]
  fields?: string[]
}

// Component Category types matching ComponentCategoryReadSchema
export interface ComponentCategoryBase {
  name: string
  description?: string
  parent_category_id?: number
  is_active?: boolean
}

export interface ComponentCategoryCreate extends ComponentCategoryBase {
  name: string
}

export interface ComponentCategoryUpdate {
  name?: string
  description?: string
  parent_category_id?: number
  is_active?: boolean
}

export interface ComponentCategoryRead
  extends ComponentCategoryBase,
    TimestampMixin {
  id: number
  full_path: string
  level: number
  is_root_category: boolean
  has_children: boolean
  component_count: number
}

export interface ComponentCategorySummary {
  id: number
  name: string
  description?: string
  parent_category_id?: number
  is_active: boolean
  component_count: number
  child_count: number
}

export interface ComponentCategoryTreeNode {
  id: number
  name: string
  description?: string
  is_active: boolean
  level: number
  component_count: number
  children: ComponentCategoryTreeNode[]
}

// Component Type types matching ComponentTypeReadSchema
export interface ComponentTypeBase {
  name: string
  description?: string
  category_id: number
  is_active?: boolean
  specifications_template?: Record<string, unknown>
  metadata?: Record<string, unknown>
}

export interface ComponentTypeCreate extends ComponentTypeBase {
  name: string
  category_id: number
}

export interface ComponentTypeUpdate {
  name?: string
  description?: string
  category_id?: number
  is_active?: boolean
  specifications_template?: Record<string, unknown>
  metadata?: Record<string, unknown>
}

export interface ComponentTypeRead extends ComponentTypeBase, TimestampMixin {
  id: number
  full_name: string
  category_path: string
  component_count: number
  has_specifications_template: boolean
  category_name?: string
}

export interface ComponentTypeSummary {
  id: number
  name: string
  description?: string
  category_id: number
  category_name: string
  is_active: boolean
  component_count: number
  has_specifications_template: boolean
  created_at: string
}

export interface ComponentTypeStats extends StatsResponse {
  total_types: number
  active_types: number
  types_by_category: Record<string, number>
  types_with_templates: number
  avg_components_per_type: number
  types_with_no_components: number
}

export interface ComponentTypeSpecificationTemplate {
  electrical?: Record<string, unknown>
  thermal?: Record<string, unknown>
  mechanical?: Record<string, unknown>
  environmental?: Record<string, unknown>
  standards_compliance?: string[]
  physical?: Record<string, unknown>
  performance?: Record<string, unknown>
}

// Category and Type list parameters
export interface ComponentCategoryListParams {
  page?: number
  size?: number
  search?: string
  parent_category_id?: number
  is_active?: boolean
  level?: number
  ordering?: string
}

export interface ComponentTypeListParams {
  page?: number
  size?: number
  search?: string
  category_id?: number
  is_active?: boolean
  has_specifications_template?: boolean
  ordering?: string
}

// Paginated responses
export interface ComponentPaginatedResponse
  extends PaginatedResponse<ComponentRead> {}
export interface ComponentSummaryPaginatedResponse
  extends PaginatedResponse<ComponentSummary> {}
export interface ComponentCategoryPaginatedResponse
  extends PaginatedResponse<ComponentCategoryRead> {}
export interface ComponentCategorySummaryPaginatedResponse
  extends PaginatedResponse<ComponentCategorySummary> {}
export interface ComponentTypePaginatedResponse
  extends PaginatedResponse<ComponentTypeRead> {}
export interface ComponentTypeSummaryPaginatedResponse
  extends PaginatedResponse<ComponentTypeSummary> {}
