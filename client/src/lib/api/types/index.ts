/**
 * API Types Barrel Export
 *
 * Central export point for all API type definitions
 */

// Common types
export * from "./common"

// Authentication and user types
export * from "./auth"
export * from "./users"

// Component management types
export * from "./components"

// Project management types
export * from "./projects"

// Task management types
export * from "./tasks"

// Authorization and RBAC types
export * from "./authorization"

// Re-export commonly used types for convenience
export type {
  // Common
  PaginatedResponse,
  TimestampMixin,
  APIError,
  ListQueryParams,
  HealthCheckResponse,
  BulkOperationResult,
  ValidationResult,
  StatsResponse,
} from "./common"

export type {
  // Auth
  UserRead,
  UserCreate,
  UserUpdate,
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  RegisterResponse,
  LogoutResponse,
  PasswordChangeRequest,
  PasswordChangeResponse,
  PasswordResetRequest,
  PasswordResetConfirm,
  PasswordResetResponse,
  UserPreference,
  UserPreferenceCreate,
  UserPreferenceUpdate,
  UserActivityLog,
  UserSession,
  UserPaginatedResponse,
  UserListParams,
} from "./auth"

export type {
  // Users (additional types from users module)
  UserRole,
  UserRoleCreate,
  UserRoleUpdate,
  UserStats,
  UserSearchParams,
  UserAdvancedSearch,
  UserBulkCreate,
  UserBulkUpdate,
  UserBulkDelete,
  UserImportData,
  UserImportResult,
  UserExportParams,
  UserProfile,
  UserProfileUpdate,
  UserNotificationPreferences,
  UserSecuritySettings,
} from "./users"

export type {
  // Components
  ComponentRead,
  ComponentCreate,
  ComponentUpdate,
  ComponentCategoryRead,
  ComponentTypeRead,
  ComponentSummary,
  ComponentPaginatedResponse,
  ComponentSummaryPaginatedResponse,
  ComponentListParams,
  ComponentAdvancedSearch,
  ComponentSearchResult,
  ComponentAdvancedSearchResponse,
  ComponentBulkCreate,
  ComponentBulkUpdate,
  ComponentValidationResult,
  ComponentStats,
  ComponentImportData,
  ComponentExportParams,
  ComponentCategoryCreate,
  ComponentCategoryUpdate,
  ComponentCategorySummary,
  ComponentCategoryTreeNode,
  ComponentCategoryPaginatedResponse,
  ComponentCategorySummaryPaginatedResponse,
  ComponentCategoryListParams,
  ComponentTypeCreate,
  ComponentTypeUpdate,
  ComponentTypeSummary,
  ComponentTypePaginatedResponse,
  ComponentTypeSummaryPaginatedResponse,
  ComponentTypeListParams,
  ComponentTypeStats,
  ComponentTypeSpecificationTemplate,
} from "./components"

export type {
  // Projects
  ProjectRead,
  ProjectCreate,
  ProjectUpdate,
  ProjectMember,
  ProjectStatus,
  ProjectSummary,
  ProjectPaginatedResponse,
  ProjectSummaryPaginatedResponse,
  ProjectListParams,
  ProjectMemberCreate,
  ProjectMemberUpdate,
  ProjectMemberPaginatedResponse,
  ProjectStats,
  ProjectSearchParams,
  ProjectAdvancedSearch,
  ProjectBulkUpdate,
  ProjectBulkStatusUpdate,
  ProjectImportData,
  ProjectExportParams,
  ProjectActivity,
  ProjectActivityPaginatedResponse,
  ProjectNotification,
} from "./projects"

export type {
  // Tasks
  TaskRead,
  TaskCreate,
  TaskUpdate,
  TaskStatus,
  TaskPriority,
  TaskSummary,
  TaskPaginatedResponse,
  TaskSummaryPaginatedResponse,
  TaskListParams,
  TaskAssignment,
  TaskAssignmentCreate,
  TaskAssignmentPaginatedResponse,
  TaskStatistics,
  TaskSearchParams,
  TaskAdvancedSearch,
  TaskBulkCreate,
  TaskBulkUpdate,
  TaskBulkStatusUpdate,
  TaskBulkAssignment,
  TaskDependency,
  TaskDependencyCreate,
  TaskComment,
  TaskCommentCreate,
  TaskCommentPaginatedResponse,
  TaskActivity,
  TaskActivityPaginatedResponse,
  TaskTimeEntry,
  TaskTimeEntryCreate,
  TaskTimeEntryPaginatedResponse,
  TaskImportData,
  TaskExportParams,
} from "./tasks"

export type {
  // Authorization
  Role,
  RoleCreate,
  RoleUpdate,
  Permission,
  PermissionCreate,
  PermissionUpdate,
  UserRoleAssignment,
  UserRoleAssignmentCreate,
  RolePermission,
  UserWithRoles,
  PermissionCheck,
  RoleHierarchy,
  PermissionsByResource,
  UserPermissionsSummary,
  RolePaginatedResponse,
  PermissionPaginatedResponse,
  UserRoleAssignmentPaginatedResponse,
  RoleFormData,
  PermissionFormData,
  UserRoleAssignmentFormData,
  AuthorizationState,
  AuthorizationActions,
  ResourceAction,
  PermissionCheckResult,
} from "./authorization"
