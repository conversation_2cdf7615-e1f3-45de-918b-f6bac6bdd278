/**
 * RBAC API client
 * 
 * Role-Based Access Control API functions for test compatibility
 */

import { 
  getAllRoles,
  createRole, 
  updateRole,
  deleteRole,
  getRoleHierarchy
} from "./endpoints/authorization"

import type { 
  Role,
  RoleCreate,
  RoleUpdate 
} from "./types/authorization"

/**
 * RBAC API client for test compatibility
 */
export const rbacApiClient = {
  getRoles: getAllRoles,
  createRole,
  updateRole,
  deleteRole,
  getRoleHierarchy,
}

// Export types for convenience
export type { Role, RoleCreate, RoleUpdate }