/**
 * User Management API Endpoints
 *
 * Provides type-safe user management API functions for admin operations
 */

import type {
  UserCreate,
  UserListParams,
  UserRead,
  UserSummary,
  UserUpdate,
} from "../types/auth"
import type { PaginatedResponse } from "../types/common"
import type {
  UserAdvancedSearch,
  UserBulkCreate,
  UserBulkDelete,
  UserBulkUpdate,
  UserExportParams,
  UserImportData,
  UserImportResult,
  UserNotificationPreferences,
  UserPaginatedResponse,
  UserProfile,
  UserProfileUpdate,
  UserRole,
  UserRoleAssignment,
  UserRoleAssignmentCreate,
  UserRoleAssignmentUpdate,
  UserRoleCreate,
  UserRoleUpdate,
  UserSearchParams,
  UserSecuritySettings,
  UserStats,
} from "../types/users"

import { apiClient } from "../client"

/**
 * User management endpoints (admin operations)
 */
export const usersApi = {
  /**
   * User CRUD operations
   */
  list: async (params?: UserListParams): Promise<UserPaginatedResponse> => {
    return apiClient.get("/users", { params })
  },

  get: async (id: number): Promise<UserRead> => {
    return apiClient.get(`/users/${id}`)
  },

  create: async (data: UserCreate): Promise<UserRead> => {
    return apiClient.post("/users", data)
  },

  update: async (id: number, data: UserUpdate): Promise<UserRead> => {
    return apiClient.put(`/users/${id}`, data)
  },

  delete: async (id: number): Promise<void> => {
    return apiClient.delete(`/users/${id}`)
  },

  /**
   * User search and filtering
   */
  search: async (params: UserSearchParams): Promise<UserPaginatedResponse> => {
    return apiClient.post("/users/search", params)
  },

  advancedSearch: async (
    criteria: UserAdvancedSearch
  ): Promise<UserPaginatedResponse> => {
    return apiClient.post("/users/advanced-search", criteria)
  },

  /**
   * User statistics
   */
  getStats: async (): Promise<UserStats> => {
    return apiClient.get("/users/stats")
  },

  /**
   * User bulk operations
   */
  bulkCreate: async (data: UserBulkCreate): Promise<UserImportResult> => {
    return apiClient.post("/users/bulk", data)
  },

  bulkUpdate: async (
    data: UserBulkUpdate
  ): Promise<{ updated: number; failed: number }> => {
    return apiClient.put("/users/bulk", data)
  },

  bulkDelete: async (
    data: UserBulkDelete
  ): Promise<{ deleted: number; failed: number }> => {
    return apiClient.post("/users/bulk-delete", data)
  },

  /**
   * User import/export
   */
  import: async (data: UserImportData[]): Promise<UserImportResult> => {
    return apiClient.post("/users/import", { users: data })
  },

  export: async (params: UserExportParams): Promise<Blob> => {
    const response = await apiClient.get("/users/export", {
      params,
      responseType: "blob",
    })
    return response
  },

  /**
   * User profile management (admin view)
   */
  getProfile: async (id: number): Promise<UserProfile> => {
    return apiClient.get(`/users/${id}/profile`)
  },

  updateProfile: async (
    id: number,
    data: UserProfileUpdate
  ): Promise<UserProfile> => {
    return apiClient.put(`/users/${id}/profile`, data)
  },

  /**
   * User preferences management
   */
  getNotificationPreferences: async (
    id: number
  ): Promise<UserNotificationPreferences> => {
    return apiClient.get(`/users/${id}/notification-preferences`)
  },

  updateNotificationPreferences: async (
    id: number,
    data: UserNotificationPreferences
  ): Promise<UserNotificationPreferences> => {
    return apiClient.put(`/users/${id}/notification-preferences`, data)
  },

  /**
   * User security management
   */
  getSecuritySettings: async (id: number): Promise<UserSecuritySettings> => {
    return apiClient.get(`/users/${id}/security`)
  },

  resetPassword: async (
    id: number
  ): Promise<{ temporary_password: string }> => {
    return apiClient.post(`/users/${id}/reset-password`)
  },

  disableTwoFactor: async (id: number): Promise<{ message: string }> => {
    return apiClient.post(`/users/${id}/disable-2fa`)
  },

  revokeAllSessions: async (id: number): Promise<{ message: string }> => {
    return apiClient.post(`/users/${id}/revoke-sessions`)
  },

  /**
   * User activation/deactivation
   */
  activate: async (id: number): Promise<UserRead> => {
    return apiClient.post(`/users/${id}/activate`)
  },

  deactivate: async (id: number): Promise<UserRead> => {
    return apiClient.post(`/users/${id}/deactivate`)
  },

  /**
   * User role management
   */
  getRoles: async (params?: {
    page?: number
    size?: number
  }): Promise<PaginatedResponse<UserRole>> => {
    return apiClient.get("/users/roles", { params })
  },

  createRole: async (data: UserRoleCreate): Promise<UserRole> => {
    return apiClient.post("/users/roles", data)
  },

  updateRole: async (id: number, data: UserRoleUpdate): Promise<UserRole> => {
    return apiClient.put(`/users/roles/${id}`, data)
  },

  deleteRole: async (id: number): Promise<void> => {
    return apiClient.delete(`/users/roles/${id}`)
  },

  /**
   * User role assignments
   */
  getUserRoles: async (userId: number): Promise<UserRoleAssignment[]> => {
    return apiClient.get(`/users/${userId}/roles`)
  },

  assignRole: async (
    data: UserRoleAssignmentCreate
  ): Promise<UserRoleAssignment> => {
    return apiClient.post("/users/role-assignments", data)
  },

  updateRoleAssignment: async (
    id: number,
    data: UserRoleAssignmentUpdate
  ): Promise<UserRoleAssignment> => {
    return apiClient.put(`/users/role-assignments/${id}`, data)
  },

  removeRoleAssignment: async (id: number): Promise<void> => {
    return apiClient.delete(`/users/role-assignments/${id}`)
  },

  /**
   * User activity monitoring
   */
  getUserActivity: async (
    userId: number,
    params?: { page?: number; size?: number }
  ): Promise<PaginatedResponse<import("../types/auth").UserActivityLog>> => {
    return apiClient.get(`/users/${userId}/activity`, { params })
  },

  getUserSessions: async (
    userId: number
  ): Promise<import("../types/auth").UserSession[]> => {
    return apiClient.get(`/users/${userId}/sessions`)
  },
}

// Export individual functions for convenience
export const {
  list,
  get,
  create,
  update,
  delete: deleteUser,
  search,
  advancedSearch,
  getStats,
  bulkCreate,
  bulkUpdate,
  bulkDelete,
  import: importUsers,
  export: exportUsers,
  getProfile,
  updateProfile,
  getNotificationPreferences,
  updateNotificationPreferences,
  getSecuritySettings,
  resetPassword,
  disableTwoFactor,
  revokeAllSessions,
  activate,
  deactivate,
  getRoles,
  createRole,
  updateRole,
  deleteRole,
  getUserRoles,
  assignRole,
  updateRoleAssignment,
  removeRoleAssignment,
  getUserActivity,
  getUserSessions,
} = usersApi

export default usersApi
