/**
 * Authorization API endpoints
 *
 * API client functions for RBAC operations including roles, permissions,
 * and user role assignments.
 */

import type {
  Permission,
  PermissionCheck,
  PermissionCreate,
  Role,
  RoleCreate,
  RoleUpdate,
  UserPermissionsSummary,
  UserRoleAssignment,
  UserRoleAssignmentCreate,
} from "../types/authorization"

import { apiClient } from "../client"

/**
 * Role Management Functions
 */

// Role operations
export async function createRole(roleData: RoleCreate): Promise<Role> {
  const response = await apiClient.post<Role>("/auth/roles", roleData)
  return response.data
}

export async function getAllRoles(): Promise<Role[]> {
  const response = await apiClient.get<Role[]>("/auth/roles")
  return response.data
}

export async function getRoleById(roleId: number): Promise<Role> {
  const response = await apiClient.get<Role>(`/auth/roles/${roleId}`)
  return response.data
}

export async function updateRole(
  roleId: number,
  roleData: RoleUpdate
): Promise<Role> {
  const response = await apiClient.patch<Role>(
    `/auth/roles/${roleId}`,
    roleData
  )
  return response.data
}

export async function deleteRole(roleId: number): Promise<void> {
  await apiClient.delete(`/auth/roles/${roleId}`)
}

// Permission operations
export async function createPermission(
  permissionData: PermissionCreate
): Promise<Permission> {
  const response = await apiClient.post<Permission>(
    "/auth/permissions",
    permissionData
  )
  return response.data
}

export async function getAllPermissions(): Promise<Permission[]> {
  const response = await apiClient.get<Permission[]>("/auth/permissions")
  return response.data
}

// Role permission assignment
export async function assignPermissionsToRole(
  roleId: number,
  permissionIds: number[]
): Promise<Role> {
  const response = await apiClient.patch<Role>(
    `/auth/roles/${roleId}/permissions`,
    permissionIds
  )
  return response.data
}

// User role assignment operations
export async function assignRoleToUser(
  userId: number,
  assignmentData: UserRoleAssignmentCreate
): Promise<UserRoleAssignment> {
  const response = await apiClient.post<UserRoleAssignment>(
    `/auth/users/${userId}/roles`,
    assignmentData
  )
  return response.data
}

export async function removeRoleFromUser(
  userId: number,
  roleId: number
): Promise<void> {
  await apiClient.delete(`/auth/users/${userId}/roles/${roleId}`)
}

// Permission checking
export async function checkUserPermission(
  userId: number,
  resource: string,
  action: string
): Promise<boolean> {
  const response = await apiClient.get<boolean>(
    `/auth/users/${userId}/permissions/${resource}/${action}`
  )
  return response.data
}

export async function getUserPermissions(userId: number): Promise<string[]> {
  const response = await apiClient.get<string[]>(
    `/auth/users/${userId}/permissions`
  )
  return response.data
}

// Role hierarchy operations
export async function getRoleHierarchy(): Promise<Role[]> {
  // For now, return all roles as a flat list since hierarchy is not yet implemented
  return getAllRoles()
}

/**
 * Authorization API object for backward compatibility
 */
export const authorizationApi = {
  createRole,
  getAllRoles,
  getRoleById,
  updateRole,
  deleteRole,
  createPermission,
  getAllPermissions,
  assignPermissionsToRole,
  assignRoleToUser,
  removeRoleFromUser,
  checkUserPermission,
  getUserPermissions,
  getRoleHierarchy,
}

/**
 * RBAC API client alias for test compatibility
 */
export const rbacApiClient = {
  getRoles: getAllRoles,
  createRole,
  updateRole,
  deleteRole,
  getRoleHierarchy,
}
