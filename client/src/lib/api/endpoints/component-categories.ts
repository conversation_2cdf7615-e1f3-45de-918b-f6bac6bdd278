/**
 * Component Category Management API Endpoints
 *
 * Provides type-safe component category management API functions
 */

import type {
  ComponentCategoryCreate,
  ComponentCategoryListParams,
  ComponentCategoryPaginatedResponse,
  ComponentCategoryRead,
  ComponentCategorySummary,
  ComponentCategorySummaryPaginatedResponse,
  ComponentCategoryTreeNode,
  ComponentCategoryUpdate,
} from "../types/components"

import { apiClient } from "../client"

/**
 * Component category management endpoints
 */
export const componentCategoriesApi = {
  /**
   * Category CRUD operations
   */
  list: async (
    params?: ComponentCategoryListParams
  ): Promise<ComponentCategoryPaginatedResponse> => {
    return apiClient.get("/component-categories", { params })
  },

  listSummary: async (
    params?: ComponentCategoryListParams
  ): Promise<ComponentCategorySummaryPaginatedResponse> => {
    return apiClient.get("/component-categories/summary", { params })
  },

  get: async (id: number): Promise<ComponentCategoryRead> => {
    return apiClient.get(`/component-categories/${id}`)
  },

  create: async (
    data: ComponentCategoryCreate
  ): Promise<ComponentCategoryRead> => {
    return apiClient.post("/component-categories", data)
  },

  update: async (
    id: number,
    data: ComponentCategoryUpdate
  ): Promise<ComponentCategoryRead> => {
    return apiClient.put(`/component-categories/${id}`, data)
  },

  delete: async (id: number): Promise<void> => {
    return apiClient.delete(`/component-categories/${id}`)
  },

  /**
   * Hierarchical operations
   */
  getTree: async (): Promise<ComponentCategoryTreeNode[]> => {
    return apiClient.get("/component-categories/tree")
  },

  getSubtree: async (
    parentId: number
  ): Promise<ComponentCategoryTreeNode[]> => {
    return apiClient.get(`/component-categories/${parentId}/subtree`)
  },

  getRootCategories: async (): Promise<ComponentCategoryRead[]> => {
    return apiClient.get("/component-categories/roots")
  },

  getChildren: async (parentId: number): Promise<ComponentCategoryRead[]> => {
    return apiClient.get(`/component-categories/${parentId}/children`)
  },

  getParents: async (id: number): Promise<ComponentCategoryRead[]> => {
    return apiClient.get(`/component-categories/${id}/parents`)
  },

  getPath: async (id: number): Promise<ComponentCategoryRead[]> => {
    return apiClient.get(`/component-categories/${id}/path`)
  },

  /**
   * Category search and filtering
   */
  search: async (query: string): Promise<ComponentCategoryRead[]> => {
    return apiClient.get("/component-categories/search", {
      params: { q: query },
    })
  },

  searchByLevel: async (level: number): Promise<ComponentCategoryRead[]> => {
    return apiClient.get("/component-categories/by-level", {
      params: { level },
    })
  },

  /**
   * Category statistics
   */
  getStats: async (): Promise<{
    total_categories: number
    active_categories: number
    root_categories: number
    max_depth: number
    categories_by_level: Record<number, number>
  }> => {
    return apiClient.get("/component-categories/stats")
  },

  getCategoryStats: async (
    id: number
  ): Promise<{
    component_count: number
    direct_component_count: number
    child_count: number
    descendant_count: number
    level: number
  }> => {
    return apiClient.get(`/component-categories/${id}/stats`)
  },

  /**
   * Category bulk operations
   */
  bulkCreate: async (
    categories: ComponentCategoryCreate[]
  ): Promise<{
    created: ComponentCategoryRead[]
    failed: Array<{ category: ComponentCategoryCreate; errors: string[] }>
  }> => {
    return apiClient.post("/component-categories/bulk", { categories })
  },

  bulkUpdate: async (
    updates: Array<{ id: number; data: ComponentCategoryUpdate }>
  ): Promise<{
    updated: number
    failed: number
  }> => {
    return apiClient.put("/component-categories/bulk", { updates })
  },

  bulkDelete: async (
    categoryIds: number[]
  ): Promise<{
    deleted: number
    failed: number
  }> => {
    return apiClient.post("/component-categories/bulk-delete", {
      category_ids: categoryIds,
    })
  },

  /**
   * Category validation
   */
  validateHierarchy: async (): Promise<{
    valid: boolean
    issues: Array<{
      category_id: number
      issue_type: string
      description: string
    }>
  }> => {
    return apiClient.get("/component-categories/validate-hierarchy")
  },

  validateMove: async (
    categoryId: number,
    newParentId: number | null
  ): Promise<{
    valid: boolean
    issues?: string[]
  }> => {
    return apiClient.post("/component-categories/validate-move", {
      category_id: categoryId,
      new_parent_id: newParentId,
    })
  },

  /**
   * Category reorganization
   */
  move: async (
    categoryId: number,
    newParentId: number | null
  ): Promise<ComponentCategoryRead> => {
    return apiClient.put(`/component-categories/${categoryId}/move`, {
      new_parent_id: newParentId,
    })
  },

  reorder: async (
    parentId: number | null,
    categoryIds: number[]
  ): Promise<{
    reordered: number
    failed: number
  }> => {
    return apiClient.put("/component-categories/reorder", {
      parent_id: parentId,
      category_ids: categoryIds,
    })
  },

  /**
   * Category import/export
   */
  export: async (format: "csv" | "xlsx" | "json" = "json"): Promise<Blob> => {
    const response = await apiClient.get("/component-categories/export", {
      params: { format },
      responseType: "blob",
    })
    return response
  },

  import: async (
    data: Array<{
      name: string
      description?: string
      parent_category_name?: string
    }>
  ): Promise<{
    imported: number
    failed: number
    errors: Array<{
      row: number
      name: string
      errors: string[]
    }>
  }> => {
    return apiClient.post("/component-categories/import", { categories: data })
  },

  /**
   * Category activation/deactivation
   */
  activate: async (id: number): Promise<ComponentCategoryRead> => {
    return apiClient.post(`/component-categories/${id}/activate`)
  },

  deactivate: async (id: number): Promise<ComponentCategoryRead> => {
    return apiClient.post(`/component-categories/${id}/deactivate`)
  },
}

// Export individual functions for convenience
export const {
  list,
  listSummary,
  get,
  create,
  update,
  delete: deleteCategory,
  getTree,
  getSubtree,
  getRootCategories,
  getChildren,
  getParents,
  getPath,
  search,
  searchByLevel,
  getStats,
  getCategoryStats,
  bulkCreate,
  bulkUpdate,
  bulkDelete,
  validateHierarchy,
  validateMove,
  move,
  reorder,
  export: exportCategories,
  import: importCategories,
  activate,
  deactivate,
} = componentCategoriesApi

export default componentCategoriesApi
