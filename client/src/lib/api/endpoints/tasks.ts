/**
 * Task Management API Endpoints
 *
 * Provides type-safe task management API functions
 */

import type {
  TaskActivity,
  TaskActivityPaginatedResponse,
  TaskAdvancedSearch,
  TaskAssignment,
  TaskAssignmentCreate,
  TaskAssignmentPaginatedResponse,
  TaskBulkAssignment,
  TaskBulkCreate,
  TaskBulkStatusUpdate,
  TaskBulkUpdate,
  TaskComment,
  TaskCommentCreate,
  TaskCommentPaginatedResponse,
  TaskCreate,
  TaskDependency,
  TaskDependencyCreate,
  TaskExportParams,
  TaskImportData,
  TaskListParams,
  TaskPaginatedResponse,
  TaskPriority,
  TaskRead,
  TaskSearchParams,
  TaskStatistics,
  TaskStatus,
  TaskSummary,
  TaskSummaryPaginatedResponse,
  TaskTimeEntry,
  TaskTimeEntryCreate,
  TaskTimeEntryPaginatedResponse,
  TaskUpdate,
} from "../types/tasks"

import { apiClient } from "../client"

/**
 * Task management endpoints
 */
export const tasksApi = {
  /**
   * Task CRUD operations
   */
  list: async (params?: TaskListParams): Promise<TaskPaginatedResponse> => {
    return apiClient.get("/tasks", { params })
  },

  listSummary: async (
    params?: TaskListParams
  ): Promise<TaskSummaryPaginatedResponse> => {
    return apiClient.get("/tasks/summary", { params })
  },

  get: async (id: number): Promise<TaskRead> => {
    return apiClient.get(`/tasks/${id}`)
  },

  getByTaskId: async (taskId: string): Promise<TaskRead> => {
    return apiClient.get(`/tasks/by-task-id/${taskId}`)
  },

  create: async (data: TaskCreate): Promise<TaskRead> => {
    return apiClient.post("/tasks", data)
  },

  update: async (id: number, data: TaskUpdate): Promise<TaskRead> => {
    return apiClient.put(`/tasks/${id}`, data)
  },

  delete: async (id: number): Promise<void> => {
    return apiClient.delete(`/tasks/${id}`)
  },

  /**
   * Task search and filtering
   */
  search: async (params: TaskSearchParams): Promise<TaskPaginatedResponse> => {
    return apiClient.post("/tasks/search", params)
  },

  advancedSearch: async (
    criteria: TaskAdvancedSearch
  ): Promise<TaskPaginatedResponse> => {
    return apiClient.post("/tasks/advanced-search", criteria)
  },

  getByProject: async (
    projectId: number,
    params?: TaskListParams
  ): Promise<TaskPaginatedResponse> => {
    return apiClient.get(`/tasks/by-project/${projectId}`, { params })
  },

  getOverdue: async (
    params?: TaskListParams
  ): Promise<TaskPaginatedResponse> => {
    return apiClient.get("/tasks/overdue", { params })
  },

  /**
   * Task statistics
   */
  getStatistics: async (): Promise<TaskStatistics> => {
    return apiClient.get("/tasks/statistics")
  },

  getProjectStatistics: async (projectId: number): Promise<TaskStatistics> => {
    return apiClient.get(`/tasks/statistics/project/${projectId}`)
  },

  getUserStatistics: async (userId: number): Promise<TaskStatistics> => {
    return apiClient.get(`/tasks/statistics/user/${userId}`)
  },

  /**
   * Task status management
   */
  updateStatus: async (
    id: number,
    status: TaskStatus,
    comment?: string
  ): Promise<TaskRead> => {
    return apiClient.put(`/tasks/${id}/status`, { status, comment })
  },

  markComplete: async (id: number, comment?: string): Promise<TaskRead> => {
    return apiClient.post(`/tasks/${id}/complete`, { comment })
  },

  markInProgress: async (id: number): Promise<TaskRead> => {
    return apiClient.post(`/tasks/${id}/start`)
  },

  /**
   * Task assignment management
   */
  getAssignments: async (id: number): Promise<TaskAssignment[]> => {
    return apiClient.get(`/tasks/${id}/assignments`)
  },

  assign: async (id: number, userIds: number[]): Promise<TaskAssignment[]> => {
    return apiClient.post(`/tasks/${id}/assign`, { user_ids: userIds })
  },

  unassign: async (id: number, userIds: number[]): Promise<void> => {
    return apiClient.post(`/tasks/${id}/unassign`, { user_ids: userIds })
  },

  reassign: async (
    id: number,
    fromUserId: number,
    toUserId: number
  ): Promise<TaskAssignment> => {
    return apiClient.post(`/tasks/${id}/reassign`, {
      from_user_id: fromUserId,
      to_user_id: toUserId,
    })
  },

  /**
   * Task bulk operations
   */
  bulkCreate: async (
    data: TaskBulkCreate
  ): Promise<{
    created: TaskRead[]
    failed: Array<{ task: TaskCreate; errors: string[] }>
  }> => {
    return apiClient.post("/tasks/bulk", data)
  },

  bulkUpdate: async (
    data: TaskBulkUpdate
  ): Promise<{ updated: number; failed: number }> => {
    return apiClient.put("/tasks/bulk", data)
  },

  bulkStatusUpdate: async (
    data: TaskBulkStatusUpdate
  ): Promise<{ updated: number; failed: number }> => {
    return apiClient.put("/tasks/bulk-status", data)
  },

  bulkAssign: async (
    data: TaskBulkAssignment
  ): Promise<{ processed: number; failed: number }> => {
    return apiClient.post("/tasks/bulk-assign", data)
  },

  bulkDelete: async (
    taskIds: number[]
  ): Promise<{ deleted: number; failed: number }> => {
    return apiClient.post("/tasks/bulk-delete", { task_ids: taskIds })
  },

  /**
   * Task dependencies
   */
  getDependencies: async (id: number): Promise<TaskDependency[]> => {
    return apiClient.get(`/tasks/${id}/dependencies`)
  },

  addDependency: async (
    id: number,
    data: TaskDependencyCreate
  ): Promise<TaskDependency> => {
    return apiClient.post(`/tasks/${id}/dependencies`, data)
  },

  removeDependency: async (id: number, dependencyId: number): Promise<void> => {
    return apiClient.delete(`/tasks/${id}/dependencies/${dependencyId}`)
  },

  /**
   * Task comments
   */
  getComments: async (
    id: number,
    params?: { page?: number; size?: number }
  ): Promise<TaskCommentPaginatedResponse> => {
    return apiClient.get(`/tasks/${id}/comments`, { params })
  },

  addComment: async (
    id: number,
    data: TaskCommentCreate
  ): Promise<TaskComment> => {
    return apiClient.post(`/tasks/${id}/comments`, data)
  },

  updateComment: async (
    id: number,
    commentId: number,
    content: string
  ): Promise<TaskComment> => {
    return apiClient.put(`/tasks/${id}/comments/${commentId}`, { content })
  },

  deleteComment: async (id: number, commentId: number): Promise<void> => {
    return apiClient.delete(`/tasks/${id}/comments/${commentId}`)
  },

  /**
   * Task activity
   */
  getActivity: async (
    id: number,
    params?: { page?: number; size?: number }
  ): Promise<TaskActivityPaginatedResponse> => {
    return apiClient.get(`/tasks/${id}/activity`, { params })
  },

  /**
   * Task time tracking
   */
  getTimeEntries: async (
    id: number,
    params?: { page?: number; size?: number }
  ): Promise<TaskTimeEntryPaginatedResponse> => {
    return apiClient.get(`/tasks/${id}/time-entries`, { params })
  },

  startTimer: async (
    id: number,
    description?: string
  ): Promise<TaskTimeEntry> => {
    return apiClient.post(`/tasks/${id}/time-entries/start`, { description })
  },

  stopTimer: async (id: number, entryId: number): Promise<TaskTimeEntry> => {
    return apiClient.post(`/tasks/${id}/time-entries/${entryId}/stop`)
  },

  addTimeEntry: async (
    id: number,
    data: TaskTimeEntryCreate
  ): Promise<TaskTimeEntry> => {
    return apiClient.post(`/tasks/${id}/time-entries`, data)
  },

  updateTimeEntry: async (
    id: number,
    entryId: number,
    data: Partial<TaskTimeEntryCreate>
  ): Promise<TaskTimeEntry> => {
    return apiClient.put(`/tasks/${id}/time-entries/${entryId}`, data)
  },

  deleteTimeEntry: async (id: number, entryId: number): Promise<void> => {
    return apiClient.delete(`/tasks/${id}/time-entries/${entryId}`)
  },

  /**
   * Task import/export
   */
  import: async (
    data: TaskImportData[]
  ): Promise<{
    imported: number
    failed: number
    errors: Array<{
      row: number
      title: string
      errors: string[]
    }>
  }> => {
    return apiClient.post("/tasks/import", { tasks: data })
  },

  export: async (params: TaskExportParams): Promise<Blob> => {
    const response = await apiClient.get("/tasks/export", {
      params,
      responseType: "blob",
    })
    return response
  },
}

// Export individual functions for convenience
export const {
  list,
  listSummary,
  get,
  getByTaskId,
  create,
  update,
  delete: deleteTask,
  search,
  advancedSearch,
  getByProject,
  getOverdue,
  getStatistics,
  getProjectStatistics,
  getUserStatistics,
  updateStatus,
  markComplete,
  markInProgress,
  getAssignments,
  assign,
  unassign,
  reassign,
  bulkCreate,
  bulkUpdate,
  bulkStatusUpdate,
  bulkAssign,
  bulkDelete,
  getDependencies,
  addDependency,
  removeDependency,
  getComments,
  addComment,
  updateComment,
  deleteComment,
  getActivity,
  getTimeEntries,
  startTimer,
  stopTimer,
  addTimeEntry,
  updateTimeEntry,
  deleteTimeEntry,
  import: importTasks,
  export: exportTasks,
} = tasksApi

export default tasksApi
