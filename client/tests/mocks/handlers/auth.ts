/**
 * Authentication API Handlers for MSW
 *
 * Provides comprehensive mocking for authentication workflows including:
 * - User login and session creation
 * - Token refresh and validation
 * - User registration
 * - Password management (change/reset)
 * - Logout and session invalidation
 */

import { http, HttpResponse } from "msw"

import {
  AuthErrors,
  AuthUtils,
  getUserFromRequest,
  MockUser,
  UserUtils,
} from "../fixtures/auth"

// Hardcode the API base URL to ensure MSW intercepts the correct endpoint
const API_BASE = "http://localhost:8000"

export const authHandlers = [
  /**
   * POST /api/v1/auth/login
   * User login
   */
  http.post(`${API_BASE}/api/v1/auth/login`, async ({ request }) => {
    const { username, password } = (await request.json()) as any

    if (!username || !password) {
      return HttpResponse.json(
        {
          error: "VALIDATION_ERROR",
          detail: "Username and password are required",
          status_code: 400,
        },
        { status: 400 }
      )
    }

    const user = UserUtils.authenticate(username, password)

    if (!user) {
      return HttpResponse.json(AuthErrors.INVALID_CREDENTIALS, { status: 401 })
    }

    if (!user.is_active) {
      return HttpResponse.json(AuthErrors.USER_INACTIVE, { status: 403 })
    }

    const tokens = await AuthUtils.createAuthTokens(user)

    // Map mock user to API UserRead shape
    const userRead = {
      id: user.id,
      name:
        user.first_name && user.last_name
          ? `${user.first_name} ${user.last_name}`
          : user.username,
      email: user.email,
      is_superuser: user.role === "admin",
      is_active: user.is_active,
      role: user.role,
      last_login: user.last_login ?? null,
      created_at: user.created_at,
      updated_at: new Date().toISOString(),
    }

    return HttpResponse.json({ ...tokens, user: userRead })
  }),

  /**
   * POST /api/v1/auth/logout
   * User logout
   */
  http.post(`${API_BASE}/api/v1/auth/logout`, async ({ request }) => {
    const authHeader = request.headers.get("Authorization")
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, {
        status: 401,
      })
    }

    // In a real scenario, we might use the refresh token from the body
    // For simplicity, we'll use the access token to identify the user and clear sessions.
    const user = await getUserFromRequest(request)
    if (user) {
      UserUtils.deleteUser(user.id) // This is a mock, so we can just delete the user
    }

    return new HttpResponse(null, { status: 204 })
  }),

  /**
   * POST /api/v1/auth/refresh
   * Refresh access token
   */
  http.post(`${API_BASE}/api/v1/auth/refresh`, async ({ request }) => {
    const { refresh_token } = (await request.json()) as any

    if (!refresh_token) {
      return HttpResponse.json(
        {
          error: "VALIDATION_ERROR",
          detail: "Refresh token is required",
          status_code: 400,
        },
        { status: 400 }
      )
    }

    const validationResult = await AuthUtils.validateRefreshToken(refresh_token)
    if (!validationResult) {
      return HttpResponse.json(AuthErrors.INVALID_TOKEN, { status: 401 })
    }

    const user = UserUtils.findById(validationResult.userId)
    if (!user || !user.is_active) {
      return HttpResponse.json(AuthErrors.USER_INACTIVE, { status: 403 })
    }

    const newAccessToken = await AuthUtils.generateAccessToken(user)
    return HttpResponse.json({
      access_token: newAccessToken,
      token_type: "bearer",
      expires_in: 15 * 60,
    })
  }),

  /**
   * POST /api/v1/auth/register
   * Register a new user
   */
  http.post(`${API_BASE}/api/v1/auth/register`, async ({ request }) => {
    const body = (await request.json()) as any

    // Current UI sends: name, email, password, confirm_password, plus optional professional fields
    if (!body?.email || !body?.password || !body?.confirm_password) {
      return HttpResponse.json(
        {
          error: "VALIDATION_ERROR",
          detail: "Email and password are required",
          status_code: 400,
        },
        { status: 400 }
      )
    }

    if (UserUtils.findByEmail(body.email)) {
      return HttpResponse.json(
        {
          error: "USER_ALREADY_EXISTS",
          detail: "User with this email or username already exists",
          status_code: 409,
        },
        { status: 409 }
      )
    }

    // Create mock user
    const [first_name, ...rest] = (body.name || "").split(" ")
    const last_name = rest.join(" ")

    const newUser = UserUtils.createUser({
      username: body.email.split("@")[0],
      email: body.email,
      first_name: first_name || body.name || "",
      last_name: last_name || "",
      role: "user",
      is_active: true,
    })

    // Map to API response shape expected by client
    const userRead = {
      id: newUser.id,
      name: body.name || `${newUser.first_name} ${newUser.last_name}`.trim(),
      email: newUser.email,
      is_superuser: false,
      is_active: true,
      role: newUser.role,
      last_login: null,
      created_at: newUser.created_at,
      updated_at: new Date().toISOString(),
    }

    return HttpResponse.json(
      { message: "Registration successful", user: userRead },
      { status: 201 }
    )
  }),

  /**
   * POST /api/v1/auth/change-password
   * Change current user's password
   */
  http.post(`${API_BASE}/api/v1/auth/change-password`, async ({ request }) => {
    const user = await getUserFromRequest(request)
    if (!user) {
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, {
        status: 401,
      })
    }

    const { old_password, new_password } = (await request.json()) as any

    // Mock password check
    if (old_password !== "password123" && old_password !== "admin123") {
      return HttpResponse.json(
        {
          error: "INVALID_CREDENTIALS",
          detail: "Incorrect old password",
          status_code: 400,
        },
        { status: 400 }
      )
    }

    UserUtils.updateUser(user.id, { password_hash: `hashed_${new_password}` })
    return new HttpResponse(null, { status: 204 })
  }),

  /**
   * GET /api/v1/auth/me
   * Get current user profile
   */
  http.get(`${API_BASE}/api/v1/auth/me`, async ({ request }) => {
    const user = await getUserFromRequest(request)
    if (!user) {
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, {
        status: 401,
      })
    }

    const { password_hash, ...userResponse } = user
    return HttpResponse.json(userResponse)
  }),
]
